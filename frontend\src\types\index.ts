// 小说相关类型
export interface Novel {
  id: string;
  title: string;
  content: string;
  characters: Character[];
  createdAt: string;
  updatedAt: string;
}

// 角色类型
export interface Character {
  id: string;
  name: string;
  voice?: string; // TTS 声音配置
  description?: string;
}

// 音频片段类型
export interface AudioSegment {
  id: string;
  text: string;
  character: Character;
  audioUrl?: string;
  duration?: number;
}

// TTS 配置类型
export interface TTSConfig {
  provider: string;
  voice: string;
  speed: number;
  pitch: number;
  volume: number;
}

// API 响应类型
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

import { useEffect } from 'react';
import { useTTSEngines } from '../hooks/useTTSEngines';
import { useTTSStore } from '../store/ttsStore';

interface TTSEngineProviderProps {
  children: React.ReactNode;
}

// 声明全局类型
declare global {
  interface Window {
    __ttsRefetch?: () => Promise<void>;
  }
}

export default function TTSEngineProvider({
  children,
}: TTSEngineProviderProps) {
  const { engines, voices, isLoading, error, refetch } = useTTSEngines();

  const {
    setEngines,
    setVoices,
    setEnginesLoading,
    setEngineError,
    updateSettings,
    settings,
  } = useTTSStore();

  // 将refetch函数挂载到全局，供其他组件使用
  useEffect(() => {
    window.__ttsRefetch = refetch;
    return () => {
      delete window.__ttsRefetch;
    };
  }, [refetch]);

  // 同步引擎数据到store
  useEffect(() => {
    setEngines(engines);
    setVoices(voices);
  }, [engines, voices, setEngines, setVoices]);

  // 同步加载状态
  useEffect(() => {
    setEnginesLoading(isLoading);
  }, [isLoading, setEnginesLoading]);

  // 同步错误状态
  useEffect(() => {
    setEngineError(error);
  }, [error, setEngineError]);

  // 当引擎数据加载完成时，验证设置并设置默认参数
  useEffect(() => {
    if (engines.length > 0 && !isLoading && !error) {
      console.log('引擎数据加载完成，验证设置:', engines.length);

      // 验证当前设置
      const currentEngineId = settings.engine;
      const currentEngine = engines.find(
        (engine) => engine.id === currentEngineId
      );

      if (currentEngine) {
        console.log('当前引擎验证通过:', currentEngine.name);

        // 检查并设置默认参数
        const defaultParams: Record<string, any> = {};
        Object.entries(currentEngine.parameters).forEach(
          ([paramName, paramConfig]) => {
            if (!(paramName in (settings.parameters || {}))) {
              defaultParams[paramName] = paramConfig.default;
            }
          }
        );

        if (Object.keys(defaultParams).length > 0) {
          console.log('设置默认参数:', defaultParams);
          updateSettings({
            parameters: {
              ...settings.parameters,
              ...defaultParams,
            },
          });
        }

        // 验证模型
        if (!currentEngine.models.includes(settings.model)) {
          console.log('当前模型无效，切换到默认模型:', currentEngine.models[0]);
          updateSettings({ model: currentEngine.models[0] });
        }
      } else {
        console.log('当前引擎无效，切换到第一个可用引擎');
        const firstAvailableEngine = engines.find(
          (engine) => engine.is_available
        );
        if (firstAvailableEngine) {
          const defaultParams: Record<string, any> = {};
          Object.entries(firstAvailableEngine.parameters).forEach(
            ([paramName, paramConfig]) => {
              defaultParams[paramName] = paramConfig.default;
            }
          );

          updateSettings({
            engine: firstAvailableEngine.id,
            model: firstAvailableEngine.models[0],
            parameters: defaultParams,
          });
        }
      }
    }
  }, [
    engines,
    isLoading,
    error,
    settings.engine,
    settings.model,
    updateSettings,
  ]);

  // 移除错误阻断逻辑，始终返回children，让界面正常显示
  // 错误信息会在具体的组件中处理和显示
  return <>{children}</>;
}

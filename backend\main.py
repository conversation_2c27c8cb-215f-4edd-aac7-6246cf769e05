#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parrot Novels 后端主程序入口
"""

import logging
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from src.api.text_api import router as text_router

# 导入API路由
from src.api.tts_api import router as tts_router
from src.api.voice_api import router as voice_router

# 导入系统初始化模块
from src.core.system_init import initialize_system, is_system_initialized

# 导入路径工具

# >> 核心修改：在模块加载时立即执行系统初始化
# 这确保了日志等配置在任何API路由注册或应用逻辑执行前完成
initialize_system()
# << 修改结束


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行的逻辑（现在可以为空，或只包含服务启动后的操作）
    print("Parrot Novels 应用生命周期已启动")
    yield
    # 关闭时的清理工作
    print("正在关闭 Parrot Novels 后端服务...")


# 创建FastAPI应用
app = FastAPI(
    title="Parrot Novels API",
    description="鹦鹉小说 - 文本转语音小说系统",
    version="1.0.0",
    debug=False,
    lifespan=lifespan,
)

# 配置CORS中间件
origins = [
    "http://localhost:5173",  # Vite开发服务器
    "http://127.0.0.1:5173",
    "http://localhost:3000",  # 备用前端端口
    "http://127.0.0.1:3000",
    "http://localhost:8000",  # Swagger UI
    "http://127.0.0.1:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger = logging.getLogger(__name__)
    logger.error(f"全局异常: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc) if app.debug else "服务器内部错误，请稍后重试",
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "Parrot Novels Backend",
        "version": "1.0.0",
        "system_initialized": is_system_initialized(),
    }


# 注册API路由
app.include_router(tts_router, prefix="/api")
app.include_router(voice_router, prefix="/api")
app.include_router(text_router, prefix="/api")


if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True, log_level="info")

import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { Toaster as HotToaster } from 'react-hot-toast';
import TTSPage from './pages/TTSPage';
import TTSEngineProvider from './components/TTSEngineProvider';

// TODO: 创建页面组件后导入
// import NovelEditorPage from './pages/NovelEditorPage';
// import AudioPlayerPage from './pages/AudioPlayerPage';

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient} data-oid="20kpnhr">
      <TTSEngineProvider data-oid="lt7hn0h">
        <Router data-oid="vtl:ea.">
          <div className="App" data-oid="4wpn6yz">
            <Routes data-oid=":1y.eo9">
              <Route
                path="/"
                element={<TTSPage data-oid="n-ik.73" />}
                data-oid="fqxy2ms"
              />

              {/* TODO: 添加更多路由 */}
              {/* <Route path="/editor" element={<NovelEditorPage />} /> */}
              {/* <Route path="/player" element={<AudioPlayerPage />} /> */}
            </Routes>

            {/* 通知组件 */}
            <Toaster position="top-right" data-oid="ktp:lv1" />
            <HotToaster position="top-center" data-oid="wyxjnfh" />
          </div>
        </Router>
      </TTSEngineProvider>
    </QueryClientProvider>
  );
}

export default App;

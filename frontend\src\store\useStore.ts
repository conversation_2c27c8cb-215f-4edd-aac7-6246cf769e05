import { create } from 'zustand';
import type { Novel, AudioSegment, TTSConfig } from '../types';

interface AppStore {
  // 小说相关状态
  novels: Novel[];
  currentNovel: Novel | null;

  // 音频相关状态
  audioSegments: AudioSegment[];
  isPlaying: boolean;
  currentSegment: AudioSegment | null;

  // TTS 配置
  ttsConfig: TTSConfig;

  // 操作方法
  setNovels: (novels: Novel[]) => void;
  setCurrentNovel: (novel: Novel | null) => void;
  addNovel: (novel: Novel) => void;
  updateNovel: (id: string, updates: Partial<Novel>) => void;
  deleteNovel: (id: string) => void;

  // 音频操作
  setAudioSegments: (segments: AudioSegment[]) => void;
  setIsPlaying: (playing: boolean) => void;
  setCurrentSegment: (segment: AudioSegment | null) => void;

  // TTS 配置
  updateTTSConfig: (config: Partial<TTSConfig>) => void;
}

export const useStore = create<AppStore>((set) => ({
  // 初始状态
  novels: [],
  currentNovel: null,
  audioSegments: [],
  isPlaying: false,
  currentSegment: null,
  ttsConfig: {
    provider: 'default',
    voice: 'default',
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
  },

  // 小说操作
  setNovels: (novels) => set({ novels }),
  setCurrentNovel: (novel) => set({ currentNovel: novel }),
  addNovel: (novel) => set((state) => ({ novels: [...state.novels, novel] })),
  updateNovel: (id, updates) =>
    set((state) => ({
      novels: state.novels.map((novel) =>
        novel.id === id ? { ...novel, ...updates } : novel
      ),
    })),
  deleteNovel: (id) =>
    set((state) => ({
      novels: state.novels.filter((novel) => novel.id !== id),
    })),

  // 音频操作
  setAudioSegments: (segments) => set({ audioSegments: segments }),
  setIsPlaying: (playing) => set({ isPlaying: playing }),
  setCurrentSegment: (segment) => set({ currentSegment: segment }),

  // TTS 配置
  updateTTSConfig: (config) =>
    set((state) => ({ ttsConfig: { ...state.ttsConfig, ...config } })),
}));

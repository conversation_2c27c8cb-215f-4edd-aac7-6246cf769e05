from fastapi import APIRouter, Depends
from fastapi.responses import FileResponse
from src.services.voice_service import VoiceService

from ..utils.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/voices", tags=["Voice"])

# 使用单例模式，避免重复初始化
_voice_service_instance = None


def get_voice_service() -> VoiceService:
    """获取Voice服务实例（单例模式）"""
    global _voice_service_instance
    if _voice_service_instance is None:
        _voice_service_instance = VoiceService()
    return _voice_service_instance


@router.get("/{voice_path:path}", response_class=FileResponse)
async def get_voice_file(
    voice_path: str, voice_service: VoiceService = Depends(get_voice_service)
):
    """根据voice路径获取声音文件

    例如: /voices/YunXi/zh-CN-YunXi-General.wav
    会读取 resources/TTS/voices/YunXi/zh-CN-YunXi-General.wav 文件
    """
    # 使用服务层获取文件信息
    file_path, media_type = voice_service.get_voice_file_info(voice_path)
    logger.info(f"获取语音文件: {file_path}")

    # 返回文件响应
    return FileResponse(
        path=str(file_path),
        media_type=media_type,
        filename=file_path.name,
    )

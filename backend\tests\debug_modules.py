#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加 backend/src 到路径
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

print("=== 检查模块导入过程 ===")

print("1. 导入前:")
print(f"   pytest in sys.modules: {'pytest' in sys.modules}")
print(f"   unittest in sys.modules: {'unittest' in sys.modules}")

print("2. 导入 indextts.infer:")
try:
    from indextts.infer import IndexTTS as IndexTTSEngine

    print("   indextts.infer 导入成功")
except ImportError as e:
    print(f"   indextts.infer 导入失败: {e}")

print(f"   pytest in sys.modules: {'pytest' in sys.modules}")
print(f"   unittest in sys.modules: {'unittest' in sys.modules}")

unittest_modules = [k for k in sys.modules.keys() if "unittest" in k]
if unittest_modules:
    print(f"   发现 unittest 相关模块: {unittest_modules}")

print("3. 导入我们的 IndexTTS:")
from tts.index.tts_impl import IndexTTS

print("   IndexTTS 导入成功")

print(f"   pytest in sys.modules: {'pytest' in sys.modules}")
print(f"   unittest in sys.modules: {'unittest' in sys.modules}")

unittest_modules = [k for k in sys.modules.keys() if "unittest" in k]
if unittest_modules:
    print(f"   发现 unittest 相关模块: {unittest_modules}")

print("4. 创建 IndexTTS 实例:")
engine = IndexTTS()
print("   IndexTTS 实例创建完成")

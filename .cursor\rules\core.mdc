---
description: 
globs: 
alwaysApply: true
---
## 规则
- 日志打印一律调用 `backend/src/utils/logging_config.py`
- 后端的单元测试一律放入 `backend/tests` 目录

## 项目目录结构

```text
ParrotNovels/
  - backend/
    - src/
      - api/
      - core/
      - db/
      - llm/
      - models/
      - schemas/
      - services/
      - text/
      - tts/
      - utils/
    - tests/
  - docs/
  - frontend/
    - public/
    - src/
      - assets/
      - components/
      - hooks/
      - pages/
      - services/
      - store/
      - types/
      - utils/
  - resources/
    - logs/
    - storage/
    - TTS/
    - UI/
```

## 前端技术栈
- pnpm 包管理
- React 19 with TypeScript
- Vite (构建工具 v6.3.5)
- Tailwind CSS (样式框架 v4.1.8)
- Zustand (状态管理 v5.0.5)
- daisyUI (UI组件 v5.0.40)
- React Router (路由) 
- React Query (数据获取 v5.77.2)
- Sonner & react-hot-toast (通知)
- lucide-react (图标)

## 前端技术架构
- ✅ TypeScript类型系统
  - AudioPlayerState接口添加playbackSpeed属性
  - TTS相关类型定义完善
- ✅ 状态管理 (Zustand)
  - TTS设置状态管理
  - 小说元数据管理
  - 音频播放器状态管理
  - 角色配置管理
- ✅ 组件架构
  - TTSPage主页面组件
  - AudioPlayer音频播放器组件
  - TTSSettingsPanel TTS设置面板
  - CharacterConfigPanel角色配置面板
  - TitleBar桌面应用标题栏

## 后端技术栈
- 开发语言：Python 3.12
- Logging 日志管理
- pytest 单元测试
- FastAPI (Python框架)
- SQLite (数据库)
- SQLAlchemy (ORM)
- LangSmith (监控和追踪)
- LangGraph (工作流图框架)
- LiteLLM (大模型接口)

## 项目结构和开发环境
- 前端根目录：frontend
- 后端根目录：backend
- 项目资源目录：resources
- 原型设计目录：resources/UI
- 操作系统：Windows 11
- 环境管理：Conda
- Conda环境名：parrot
- Python路径：D:/soft/Lang/miniconda3/envs/parrot/python.exe
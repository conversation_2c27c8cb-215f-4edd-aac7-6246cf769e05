import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  TTSSettings,
  NovelMeta,
  Character,
  AudioPlayerState,
  VoiceOption,
  TTSEngine,
} from '../types/tts';

interface TTSStore {
  // 状态
  settings: TTSSettings;
  novel: NovelMeta;
  characters: Character[];
  audioPlayer: AudioPlayerState;
  availableEngines: TTSEngine[];
  availableVoices: VoiceOption[];
  isProcessing: boolean;
  isLoadingEngines: boolean;
  engineError: string | null;
  currentAudioUrl: string | null; // 当前生成的音频URL

  // >> 新增：小说处理流程状态
  isProcessingNovel: boolean; // 是否正在进行角色识别与对话分割
  showSplitView: boolean; // 是否显示分栏视图
  processingProgress: number; // 处理进度 (0-100)
  processingStatus: string; // 当前处理状态的描述文本
  processingResult: any; // 处理完成后的JSON结果
  processingError: string | null; // 处理过程中的错误信息
  // << 新增结束

  // Actions
  updateSettings: (settings: Partial<TTSSettings>) => void;
  updateNovel: (novel: Partial<NovelMeta>) => void;
  updateCharacter: (characterId: string, updates: Partial<Character>) => void;
  addCharacter: (character: Character) => void;
  removeCharacter: (characterId: string) => void;
  updateAudioPlayer: (state: Partial<AudioPlayerState>) => void;
  setProcessing: (processing: boolean) => void;
  setEngines: (engines: TTSEngine[]) => void;
  setVoices: (voices: VoiceOption[]) => void;
  setEnginesLoading: (loading: boolean) => void;
  setEngineError: (error: string | null) => void;
  setCurrentAudioUrl: (url: string | null) => void; // 设置当前音频URL
  resetProject: () => void;
  processNovel: () => Promise<void>; // 修改 recognizeCharacters 为通用的 processNovel
  getCurrentEngineModels: () => string[];
  getCurrentEngine: () => TTSEngine | undefined;
  getVoicesByCurrentEngine: () => VoiceOption[];

  // >> 新增：小说处理流程 Actions
  startNovelProcessing: () => void;
  setNovelProcessingProgress: (progress: number, status: string) => void;
  setNovelProcessingResult: (result: any) => void;
  setNovelProcessingError: (error: string | null) => void;
  finishNovelProcessing: () => void;
  resetNovelProcessing: () => void;
  // << 新增结束
}

// 默认状态
const defaultSettings: TTSSettings = {
  engine: 'index',
  model: 'IndexTTS-1.5', // 使用engine.json中的第一个模型
  mode: 'multi-character', // 默认多角色声音模式
  singleVoiceId: 'zh-CN-Yunxi', // 默认选择云希
  singleVoiceStyle: '通用', // 默认样式
  parameters: {
    // 默认语音参数会在引擎加载后从后端获取
  }, // 引擎参数
};

const defaultNovel: NovelMeta = {
  title: '',
  category: '都市言情',
  content: ``,
};

const defaultCharacters: Character[] = [
  // 默认为空，需要通过AI识别生成
];

const defaultAudioPlayer: AudioPlayerState = {
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  currentCharacter: '',
  currentText: '请先生成语音内容',
  playbackSpeed: 1.0,
  volume: 0.5, // 默认音量为50%
};

export const useTTSStore = create<TTSStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      settings: defaultSettings,
      novel: defaultNovel,
      characters: defaultCharacters,
      audioPlayer: defaultAudioPlayer,
      availableEngines: [],
      availableVoices: [],
      isProcessing: false,
      isLoadingEngines: true,
      engineError: null,
      currentAudioUrl: null,

      // >> 新增：小说处理流程状态初始化
      isProcessingNovel: false,
      showSplitView: false,
      processingProgress: 0,
      processingStatus: '',
      processingResult: null,
      processingError: null,
      // << 新增结束

      // Actions
      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),

      updateNovel: (novelUpdates) =>
        set((state) => ({
          novel: { ...state.novel, ...novelUpdates },
        })),

      updateCharacter: (characterId, updates) =>
        set((state) => ({
          characters: state.characters.map((char) =>
            char.id === characterId ? { ...char, ...updates } : char
          ),
        })),

      addCharacter: (character) =>
        set((state) => ({
          characters: [...state.characters, character],
        })),

      removeCharacter: (characterId) =>
        set((state) => ({
          characters: state.characters.filter(
            (char) => char.id !== characterId
          ),
        })),

      updateAudioPlayer: (playerUpdates) =>
        set((state) => ({
          audioPlayer: { ...state.audioPlayer, ...playerUpdates },
        })),

      setProcessing: (processing) => set({ isProcessing: processing }),

      // >> 新增：小说处理流程 Actions 实现
      startNovelProcessing: () =>
        set({
          isProcessingNovel: true,
          showSplitView: true, // 点击处理后立即显示分栏
          processingProgress: 0,
          processingStatus: '准备开始处理...',
          processingResult: null,
          processingError: null,
        }),

      setNovelProcessingProgress: (progress, status) =>
        set({
          processingProgress: progress,
          processingStatus: status,
        }),

      setNovelProcessingResult: (result) =>
        set({
          processingResult: result,
          processingStatus: '处理完成！',
          processingProgress: 100,
        }),

      setNovelProcessingError: (error) =>
        set({
          isProcessingNovel: false,
          processingError: error,
          processingStatus: '处理出错',
        }),

      finishNovelProcessing: () => set({ isProcessingNovel: false }),

      resetNovelProcessing: () =>
        set({
          isProcessingNovel: false,
          showSplitView: false,
          processingProgress: 0,
          processingStatus: '',
          processingResult: null,
          processingError: null,
        }),

      setEngines: (engines) => set({ availableEngines: engines }),

      setVoices: (voices) => set({ availableVoices: voices }),

      setEnginesLoading: (loading) => set({ isLoadingEngines: loading }),

      setEngineError: (error) => set({ engineError: error }),

      setCurrentAudioUrl: (url) => set({ currentAudioUrl: url }),

      resetProject: () =>
        set({
          settings: defaultSettings,
          novel: defaultNovel,
          characters: defaultCharacters,
          audioPlayer: defaultAudioPlayer,
          isProcessing: false,
          currentAudioUrl: null,
          // 同时重置小说处理状态
          isProcessingNovel: false,
          showSplitView: false,
          processingProgress: 0,
          processingStatus: '',
          processingResult: null,
          processingError: null,
        }),

      getCurrentEngine: () => {
        const currentEngineId = get().settings.engine;
        return get().availableEngines.find(
          (engine) => engine.id === currentEngineId
        );
      },

      getCurrentEngineModels: () => {
        const currentEngine = get().getCurrentEngine();
        return currentEngine?.models || [];
      },

      getVoicesByCurrentEngine: () => {
        const currentEngine = get().getCurrentEngine();
        if (!currentEngine) return [];

        return currentEngine.voices.map((voice) => ({
          id: voice.id_name,
          name: voice.display_name,
          gender: voice.gender.toLowerCase() as 'male' | 'female',
          description: voice.description,
          language: voice.locale,
          styles: Object.keys(voice.styles),
          stylesPaths: voice.styles, // 保存完整的样式路径映射
        }));
      },

      // 用新的 processNovel 替换旧的 recognizeCharacters
      processNovel: async () => {
        const novel = get().novel;
        if (!novel.content.trim()) {
          console.warn('小说内容为空，已中止处理。');
          return;
        }

        get().startNovelProcessing();

        try {
          const { textApi } = await import('../services/textApi');

          await textApi.processNovelStream({
            content: novel.content,
            title: novel.title,
            onUpdate: (data) => {
              console.log('SSE Update:', data);
              const { node, progress, status, characters } = data;

              // 更新处理进度和状态
              get().setNovelProcessingProgress(
                progress,
                status || node || '处理中...'
              );

              // 如果返回了角色信息，可以更新到角色列表
              if (characters && characters.length > 0) {
                const newCharacters: Character[] = characters.map(
                  (char: any, index: number) => ({
                    id: `char-${Date.now()}-${index}`,
                    name: char.name,
                    emoji:
                      char.gender === '男'
                        ? '👨'
                        : char.gender === '女'
                          ? '👩'
                          : '👤',
                    percentage: 0, // 进度暂时不处理
                    voiceId: '',
                    voiceStyle: '通用',
                  })
                );
                set({ characters: newCharacters });
              }
            },
            onFinal: (data) => {
              console.log('SSE Final:', data);
              get().setNovelProcessingResult(data);

              // 从最终结果中提取角色信息并更新到 store
              if (data && data.characters && Array.isArray(data.characters)) {
                const newCharacters: Character[] = data.characters.map(
                  (char: any, index: number) => ({
                    id: `char-${Date.now()}-${index}`,
                    name: char.name,
                    emoji:
                      char.gender === '男'
                        ? '👨'
                        : char.gender === '女'
                          ? '👩'
                          : char.name === '旁白'
                            ? '📖'
                            : '👤',
                    percentage: char.dialogue_count
                      ? Math.round((char.dialogue_count / 100) * 100)
                      : 0, // 暂时简单计算百分比
                    voiceId: '',
                    voiceStyle: '通用',
                  })
                );
                set({ characters: newCharacters });
                console.log('从最终结果中更新了角色列表:', newCharacters);
              }
            },
            onError: (error) => {
              console.error('SSE Error:', error);
              get().setNovelProcessingError(error.message || '未知流处理错误');
            },
            onClose: () => {
              console.log('SSE connection closed.');
              get().finishNovelProcessing();
            },
          });
        } catch (error: any) {
          console.error('调用小说处理流失败:', error);
          get().setNovelProcessingError(
            error.message || '启动处理时发生未知错误'
          );
        }
      },
    }),
    {
      name: 'tts-store',
    }
  )
);

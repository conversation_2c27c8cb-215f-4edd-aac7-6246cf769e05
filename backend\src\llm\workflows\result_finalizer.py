"""结果最终化模块

包含结果处理、主题分析等相关功能
"""

from datetime import datetime

from ...utils.logging_config import get_logger

logger = get_logger(__name__)

from .langsmith_config import log_workflow_metrics, with_langsmith_tracing  # noqa: E402


class ResultFinalizer:
    """结果最终化处理器"""

    def __init__(self, workflow_instance):
        """
        初始化结果最终化处理器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    @property
    def llm_client(self):
        """延迟访问llm_client"""
        return self.workflow.llm_client

    @with_langsmith_tracing(
        run_name="finalize_results",
        tags=["completion", "summary"],
        exclude_fields=[
            "novel_content",
            "chapter_contents",
            "overall_analysis",
            "chapters",
        ],
    )
    def finalize_results(self, state):
        """完成处理节点"""
        logger.info(f"{state['workflow_id']}: 完成小说处理")

        # 记录完成指标
        log_workflow_metrics(
            state,
            "finalize_results",
            {
                "final_quality_score": state.get("quality_scores", {}).get(
                    "overall", 0
                ),
                "total_retry_count": state.get("retry_count", 0),
                "processing_time_seconds": (
                    datetime.now() - datetime.fromisoformat(state["created_at"])
                ).total_seconds(),
            },
        )

        try:
            # 检查是否已有完整的分析结果
            if state.get("overall_analysis"):
                logger.info(f"{state['workflow_id']}: 使用已有的完整分析结果")
                return {
                    **state,
                    "step": "completed",
                    "status": "completed",
                    "updated_at": datetime.now().isoformat(),
                    "processing_progress": 1.0,
                }
            logger.info(f"{state['workflow_id']}: 生成简单整体分析完成")

            return {
                **state,
                "step": "completed",
                "status": "completed",
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 1.0,
            }
        except Exception as e:
            # 如果整体分析失败，仍然返回成功状态，但不包含整体分析
            logger.info(f"{state['workflow_id']}: 整体分析失败: {str(e)}")
            return {
                **state,
                "step": "completed",
                "status": "completed",
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 1.0,
            }

import json
from typing import Any, Dict, Iterator, Optional

import requests

from ..engine import TTSEngineBase

try:
    from src.schemas.tts_schema import TTSVoice
except ImportError:
    pass


class MinimaxTTS(TTSEngineBase):
    """Minimax TTS实现"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.group_id = self.config.get("group_id", "")
        self.api_key = self.config.get("api_key", "")
        self.model = self.config.get("model", "speech-02-turbo")
        self.voice_id = self.config.get("voice_id", "male-qn-qingse")
        self.url = f"https://api.minimaxi.chat/v1/t2a_v2?GroupId={self.group_id}"

    def _build_headers(self) -> Dict[str, str]:
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

    def _build_body(
        self,
        text: str,
        stream: bool = False,
        voice_id_name: Optional[str] = None,
        voice_style_name: Optional[str] = None,
        **kwargs,
    ) -> str:
        voice_setting = {
            "voice_id": voice_id_name or kwargs.get("voice_id", self.voice_id),
            "speed": kwargs.get("speed", 1.0),
            "vol": kwargs.get("volume", 1.0),
            "pitch": kwargs.get("pitch", 0),
        }

        # 如果提供了voice_style_name，可以在这里根据需要处理
        # Minimax API 可能需要根据voice_style_name调整voice_setting
        if voice_style_name:
            # 这里可以根据voice_style_name调整voice_setting，比如调整音色或其他参数
            pass

        audio_setting = {
            "sample_rate": kwargs.get("sample_rate", 32000),
            "bitrate": kwargs.get("bitrate", 128000),
            "format": kwargs.get("format", "mp3"),
            "channel": kwargs.get("channel", 1),
        }

        body = {
            "model": kwargs.get("model", self.model),
            "text": text,
            "stream": stream,
            "voice_setting": voice_setting,
            "audio_setting": audio_setting,
        }

        return json.dumps(body)

    def synthesize(
        self,
        text: str,
        voice_id_name: str,
        voice_style_name: str,
        output_path: Optional[str] = None,
        **kwargs,
    ) -> bytes:
        """合成语音"""
        headers = self._build_headers()
        body = self._build_body(
            text,
            stream=False,
            voice_id_name=voice_id_name,
            voice_style_name=voice_style_name,
            **kwargs,
        )

        response = requests.post(self.url, headers=headers, data=body)
        response.raise_for_status()

        result = response.json()
        if "data" in result and "audio" in result["data"]:
            audio_data = bytes.fromhex(result["data"]["audio"])

            if output_path:
                self.save_audio(audio_data, output_path)

            return audio_data

        raise Exception(f"TTS failed: {result}")

    def synthesize_stream(
        self, text: str, voice_id_name: str, voice_style_name: str, **kwargs
    ) -> Iterator[bytes]:
        """流式合成语音"""
        headers = self._build_headers()
        body = self._build_body(
            text,
            stream=True,
            voice_id_name=voice_id_name,
            voice_style_name=voice_style_name,
            **kwargs,
        )

        response = requests.post(self.url, headers=headers, data=body, stream=True)
        response.raise_for_status()

        for chunk in response.raw:
            if chunk and chunk[:5] == b"data:":
                try:
                    data = json.loads(chunk[5:])
                    if "data" in data and "audio" in data["data"]:
                        audio_chunk = bytes.fromhex(data["data"]["audio"])
                        yield audio_chunk
                except json.JSONDecodeError:
                    continue

    def get_configurable_parameters(self) -> Dict[str, Dict[str, Any]]:
        """获取Minimax TTS可配置的参数信息

        Returns:
            参数配置字典，包含speed、volume、pitch等参数
        """
        return {
            "speed": {
                "type": "float",
                "range": [0.5, 2.0],
                "default": 1.0,
                "description": "语速调节，0.5为慢速，2.0为快速",
            },
            "volume": {
                "type": "float",
                "range": [0.1, 2.0],
                "default": 1.0,
                "description": "音量调节，0.1为最小音量，2.0为最大音量",
            },
            "pitch": {
                "type": "int",
                "range": [-12, 12],
                "default": 0,
                "description": "音调调节，-12到12的半音级数调节",
            },
            "sample_rate": {
                "type": "int",
                "range": [16000, 48000],
                "default": 32000,
                "description": "采样率，支持16000、24000、32000、48000",
            },
            "bitrate": {
                "type": "int",
                "range": [64000, 320000],
                "default": 128000,
                "description": "比特率，音频质量控制",
            },
        }

    def initialize(self) -> bool:
        """初始化 Minimax TTS 引擎

        Returns:
            bool: True 表示初始化成功，False 表示初始化失败
        """
        # Minimax TTS 是基于API的服务，无需本地初始化
        # 只需要检查必要的配置是否存在
        if not self.group_id or not self.api_key:
            return False
        return True

    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息

        Returns:
            引擎信息字典
        """
        return {
            "name": "MinimaxTTS",
            "version": "1.0.0",
            "description": "Minimax 海螺AI语音合成引擎，支持高质量语音生成",
            "models": [
                "speech-02-hd",
                "speech-02-turbo",
                "speech-01-hd",
                "speech-01-turbo",
            ],
            "type": "cloud",
            "supported_languages": ["zh-CN", "en-US"],
            "supports_streaming": True,
        }

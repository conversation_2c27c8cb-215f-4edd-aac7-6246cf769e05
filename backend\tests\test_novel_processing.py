#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试小说处理工作流
"""

import json
import sys
from pathlib import Path

import pytest
from dotenv import load_dotenv

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
backend_src = project_root / "backend" / "src"
sys.path.insert(0, str(backend_src))

# 加载环境变量
load_dotenv(project_root / ".env")


# 确保能正确导入模块
def ensure_imports():
    """确保所有必要的模块都能正确导入"""
    try:
        # 测试基础导入和配置日志系统
        import utils.logging_config  # type: ignore

        # 配置日志系统，显示 INFO 级别的日志
        utils.logging_config.setup_app_logging(level="INFO", quiet=False)
        print("[OK] 成功导入并配置 utils.logging_config")

        # 直接导入配置模块，避免通过llm包导入
        from llm.config import ModelProvider, TaskType  # type: ignore

        print("[OK] 成功导入 llm.config")

        # 直接导入工作流模块
        from llm.workflows.novel_processing import NovelProcessingWorkflow

        print("[OK] 成功导入 llm.workflows.novel_processing")

        return True
    except ImportError as e:
        print(f"[FAIL] 导入失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_real_llm_workflow():
    """
    测试真实LLM调用的完整工作流（需要配置LLM）
    """
    # 首先确保模块可以导入
    if not ensure_imports():
        pytest.skip("模块导入失败，跳过测试")
        return

    try:
        from llm.workflows.novel_processing import NovelProcessingWorkflow

        # 小说文件路径
        novel_file_path = project_root / ".dev" / "小说示例" / "倚天.txt"

        if not novel_file_path.exists():
            pytest.skip("小说文件不存在")
            return

        # 尝试不同编码读取小说内容
        novel_content = None

        try:
            with open(novel_file_path, "r", encoding="utf-8") as f:
                novel_content = f.read()
        except UnicodeDecodeError as e:
            print(f"读取小说内容失败 {e}")
            return

        print(f"\n{'=' * 60}")
        print("真实LLM工作流测试")
        print(f"{'=' * 60}")
        print(f"小说内容长度: {len(novel_content)} 字符")

        # 创建工作流
        workflow = NovelProcessingWorkflow()

        # 检查是否可以初始化LLM客户端
        try:
            # 尝试访问LLM客户端
            if hasattr(workflow, "llm_client") and workflow.llm_client:
                print("[OK] LLM客户端可用，开始真实调用测试")
            else:
                pytest.skip("LLM客户端未配置，跳过真实调用测试")
                return
        except:
            pytest.skip("LLM客户端初始化失败，跳过真实调用测试")
            return

        # 初始化状态（自动从文件名提取标题）
        novel_title = novel_file_path.stem  # 获取去除后缀的文件名
        state = workflow.get_initial_state(
            novel_content=novel_content,
            novel_title=novel_title,
            source_file=str(novel_file_path),
        )

        # 执行完整工作流
        print("开始执行完整工作流...")

        # 步骤1: 输入验证
        state = workflow.validate_input(state)
        print(f"1. 输入验证: {state['step']}")

        # 步骤2: 角色和章节分析（真实LLM调用）
        print("2. 执行角色和章节分析（可能需要较长时间）...")
        state = workflow.analyze_characters(state)

        if state.get("error"):
            print(f"[FAIL] 分析失败: {state['error']}")
            pytest.fail("LLM分析失败")
            return

        print("[OK] 分析完成:")
        print(f"   - 识别角色: {len(state.get('characters', []))} 个")
        print(f"   - 分析章节: {len(state.get('chapters', []))} 个")

        # 输出角色信息
        characters = state.get("characters", [])
        for char in characters:
            print(f"   角色: {char.get('name', '未知')} ({char.get('type', '未知')})")

        # 输出章节信息
        chapters = state.get("chapters", [])
        for chapter in chapters:
            chapter_title = chapter.get(
                "title", f"第{chapter.get('chapter_index', '?')}章"
            )
            chapter_summary = chapter.get("summary", "无摘要")[:50]
            print(f"   {chapter_title}: {chapter_summary}...")

        # 步骤3: 章节分割
        state = workflow.split_chapters(state)
        print(f"3. 章节分割: {state['step']}")

        # 步骤4: 跳过对话标注
        state = {
            **state,
            "step": "quality_check",
            "annotated_chapters": state["chapters"],
            "processing_progress": 0.8,
        }
        print("4. 对话标注: 跳过")

        # 步骤5: 质量检查
        state = workflow.quality_check(state)
        print(f"5. 质量检查: {state.get('quality_scores', {}).get('overall', 0):.2f}")

        # 步骤6: 决策
        decision = workflow.decide_after_quality_check(state)
        print(f"6. 决策: {decision}")

        # 步骤7: 完成
        if decision == "finalize":
            final_state = workflow.finalize_results(state)
            print(f"7. 完成: {final_state['status']}")

            # 输出最终结果
            print(f"\n{'=' * 60}")
            print("最终分析结果")
            print(f"{'=' * 60}")

            result = {
                "characters": final_state.get("characters", []),
                "chapters": final_state.get("chapters", []),
                "overall_analysis": final_state.get("overall_analysis", {}),
            }

            print(json.dumps(result, ensure_ascii=False, indent=2))
            print("\n[OK] 真实LLM工作流测试成功！")
        else:
            print(f"[FAIL] 质量检查失败: {decision}")

    except ImportError as e:
        pytest.skip(f"无法导入必需模块: {e}")
    except Exception as e:
        print(f"[FAIL] 真实LLM测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        # 不抛出异常，因为LLM可能未配置


def test_llm_manager_workflow():
    """
    使用 LLMManager 测试完整的小说处理工作流
    """
    # 首先确保模块可以导入
    if not ensure_imports():
        pytest.skip("模块导入失败，跳过测试")
        return

    try:
        from llm.config import LLMConfig
        from llm.manager import LLMManager

        # 小说文件路径
        novel_file_path = project_root / ".dev" / "小说示例" / "倚天.txt"

        if not novel_file_path.exists():
            pytest.skip("小说文件不存在")
            return

        # 读取小说内容
        novel_content = None
        try:
            with open(novel_file_path, "r", encoding="utf-8") as f:
                novel_content = f.read()
        except UnicodeDecodeError as e:
            print(f"读取小说内容失败 {e}")
            return

        print(f"\n{'=' * 60}")
        print("LLMManager 工作流测试")
        print(f"{'=' * 60}")
        print(f"小说内容长度: {len(novel_content)} 字符")

        # 创建 LLMManager
        try:
            config = LLMConfig()
            manager = LLMManager(config)
            print("[OK] LLMManager 初始化成功")
        except Exception as e:
            pytest.skip(f"LLMManager 初始化失败: {e}")
            return

        # 列出可用工作流
        workflows = manager.list_workflows()
        print(f"可用工作流: {workflows}")

        if "novel_processing" not in workflows:
            pytest.skip("novel_processing 工作流不可用")
            return

        # 启动工作流
        print("\n1. 启动小说处理工作流...")
        novel_title = novel_file_path.stem  # 获取去除后缀的文件名
        start_result = manager.start_workflow(
            "novel_processing",
            novel_content=novel_content,
            novel_title=novel_title,
            source_file=str(novel_file_path),
        )

        if not start_result["success"]:
            pytest.fail(f"启动工作流失败: {start_result['error']}")
            return

        workflow_id = start_result["workflow_id"]
        print(f"[OK] 工作流启动成功，ID: {workflow_id}")

        # 检查初始状态
        initial_state = start_result["initial_state"]
        print(f"初始步骤: {initial_state['step']}")
        print("预期步骤: split_chapters")

        if initial_state["step"] != "split_chapters":
            print(f"[WARN] 初始步骤不是预期的 split_chapters: {initial_state['step']}")

        # 流式运行工作流
        print("\n2. 流式运行工作流...")
        try:
            step_count = 0
            for step_result in manager.stream_workflow(workflow_id):
                if not step_result["success"]:
                    print(f"[FAIL] 步骤失败: {step_result['error']}")
                    break

                step_data = step_result.get("step_result", {})

                # 调试信息：显示原始数据结构
                # if step_count < 3:  # 只显示前3个步骤的调试信息
                #     print(f"   [DEBUG] 原始步骤数据类型: {type(step_data)}")
                #     print(f"   [DEBUG] 原始步骤数据: {step_data}")

                # LangGraph 的 stream 输出格式通常是 {node_name: node_output}
                # 我们需要从中提取实际的状态数据
                actual_step_data = None
                step_name = "未知步骤"

                if isinstance(step_data, dict):
                    # 查找包含状态信息的节点输出
                    for node_name, node_output in step_data.items():
                        if isinstance(node_output, dict) and "step" in node_output:
                            actual_step_data = node_output
                            step_name = node_output.get("step", node_name)
                            break

                    # 如果没有找到状态信息，但有节点名称，使用节点名称作为步骤名
                    if not actual_step_data and step_data:
                        # 获取第一个键作为步骤名
                        first_key = next(iter(step_data.keys()), "未知步骤")
                        first_value = step_data.get(first_key, {})
                        if isinstance(first_value, dict):
                            actual_step_data = first_value
                            step_name = first_value.get("step", first_key)
                        else:
                            step_name = first_key
                            actual_step_data = {}

                if actual_step_data:
                    progress = actual_step_data.get("processing_progress", 0)
                    status = actual_step_data.get("status", "未知状态")
                else:
                    progress = 0
                    status = "未知状态"
                    # 如果没有实际数据，尝试从step_data本身获取
                    if isinstance(step_data, dict) and "step" in step_data:
                        step_name = step_data.get("step", "未知步骤")
                        progress = step_data.get("processing_progress", 0)
                        status = step_data.get("status", "未知状态")
                        actual_step_data = step_data

                print(
                    f"   步骤 {step_count + 1}: {step_name} (进度: {progress:.1%}, 状态: {status})"
                )

                # 显示错误信息
                if actual_step_data and actual_step_data.get("error"):
                    print(f"   [ERROR] {actual_step_data['error']}")
                    break

                # 显示步骤特定信息
                if actual_step_data:
                    if step_name == "split_chapters":
                        chapter_count = actual_step_data.get("total_chapters", 0)
                        print(f"           检测到 {chapter_count} 个章节")
                    elif step_name == "analyze_characters":
                        char_count = len(actual_step_data.get("characters", []))
                        chapter_count = len(actual_step_data.get("chapters", []))
                        print(
                            f"           分析出 {char_count} 个角色, {chapter_count} 个章节"
                        )
                    elif step_name == "quality_check":
                        overall_score = actual_step_data.get("quality_scores", {}).get(
                            "overall", 0
                        )
                        print(f"           质量分数: {overall_score:.2f}")

                step_count += 1

                # 防止无限循环
                if step_count > 20:
                    print("[WARN] 步骤数量过多，中止测试")
                    break

            print(f"[OK] 流式执行完成，总共 {step_count} 个步骤")

        except Exception as e:
            print(f"[FAIL] 流式运行失败: {str(e)}")
            import traceback

            traceback.print_exc()

        # 获取最终状态
        print("\n3. 获取最终状态...")
        final_state = manager.get_workflow_state(workflow_id)

        if final_state:
            print(f"最终状态: {final_state.get('status', '未知')}")
            print(f"最终步骤: {final_state.get('step', '未知')}")
            print(f"最终进度: {final_state.get('processing_progress', 0):.1%}")

            # 显示结果统计
            characters = final_state.get("characters", [])
            chapters = final_state.get("chapters", [])
            annotated_chapters = final_state.get("annotated_chapters", [])

            print("处理结果:")
            print(f"  - 角色数量: {len(characters)}")
            print(f"  - 章节数量: {len(chapters)}")
            print(f"  - 标注章节: {len(annotated_chapters)}")

            # 显示部分角色信息
            if characters:
                print("  - 角色列表:")
                for i, char in enumerate(characters[:3]):  # 只显示前3个
                    name = char.get("name", "未知")
                    char_type = char.get("type", "未知")
                    print(f"    {i + 1}. {name} ({char_type})")
                if len(characters) > 3:
                    print(f"    ... 还有 {len(characters) - 3} 个角色")
        else:
            print("[WARN] 无法获取最终状态")

        # 清理工作流
        print("\n4. 清理工作流...")
        cleanup_count = manager.cleanup_completed_workflows(0)  # 立即清理
        print(f"[OK] 清理了 {cleanup_count} 个已完成的工作流")

        print("\n[OK] LLMManager 工作流测试完成！")

    except ImportError as e:
        pytest.skip(f"无法导入必需模块: {e}")
    except Exception as e:
        print(f"[FAIL] LLMManager 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        # 不抛出异常，因为LLM可能未配置


def split_chapters_by_numbers(content: str):
    """根据单独行的数字分割章节"""

    # 查找单独行的数字（章节分割符）
    lines = content.split("\n")
    chapter_starts = []

    for i, line in enumerate(lines):
        stripped_line = line.strip()
        # 检查是否为单独行的数字
        if stripped_line.isdigit():
            chapter_starts.append(i)

    if not chapter_starts:
        # 如果没有找到数字分割符，返回整个内容作为一章
        return [content.strip()]

    chapters = []

    # 处理第一个数字分割符之前的内容（序言或第0章）
    if chapter_starts[0] > 0:
        prologue_lines = lines[: chapter_starts[0]]
        prologue_content = "\n".join(prologue_lines).strip()
        if prologue_content:  # 只添加非空内容
            chapters.append(prologue_content)

    # 处理数字分割符之后的章节
    for i, start_idx in enumerate(chapter_starts):
        # 确定章节结束位置
        if i + 1 < len(chapter_starts):
            end_idx = chapter_starts[i + 1]
        else:
            end_idx = len(lines)

        # 提取章节内容（跳过分割符数字行）
        chapter_lines = lines[start_idx + 1 : end_idx]
        chapter_content = "\n".join(chapter_lines).strip()

        if chapter_content:  # 只添加非空章节
            chapters.append(chapter_content)

    return chapters if chapters else [content.strip()]


def test_chapter_split():
    # 读取倚天.txt文件
    file_path = project_root / "docs" / "小说示例" / "倚天.txt"
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    # 测试分割
    chapters = split_chapters_by_numbers(content)

    print(f"\n总章节数: {len(chapters)}")
    print("=" * 50)

    for i, chapter in enumerate(chapters):
        print(f"第{i}章 (长度: {len(chapter)}字符):")
        print(chapter)
        print("-" * 30)


def test_workflow():
    print("开始测试小说处理工作流...")
    # 首先配置日志系统
    try:
        import utils.logging_config

        utils.logging_config.setup_app_logging(level="DEBUG", quiet=False)
        print("[OK] 日志系统配置完成（DEBUG 级别）")
    except Exception as e:
        print(f"[WARN] 日志系统配置失败: {e}")

    print("\n检查模块导入...")
    if not ensure_imports():
        print("[FAIL] 模块导入失败，无法继续测试")
        sys.exit(1)

    # try:
    #     test_real_llm_workflow()
    # except Exception as e:
    #     print(f"自定义LLM测试跳过或失败: {e}")

    print("\n" + "=" * 60)
    print("开始 LLMManager 工作流测试...")
    try:
        test_llm_manager_workflow()
    except Exception as e:
        print(f"LLMManager 测试跳过或失败: {e}")

    print("\n所有测试完成！")


if __name__ == "__main__":
    # test_chapter_split()
    test_workflow()

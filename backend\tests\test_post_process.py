"""测试后处理节点功能"""

import pytest

from backend.src.llm.workflows.post_processor import PostProcessor


class MockWorkflow:
    """模拟工作流实例"""

    def handle_error(self, state, error):
        return {**state, "error": str(error), "status": "failed"}


@pytest.fixture
def post_processor():
    """创建后处理器实例"""
    mock_workflow = MockWorkflow()
    return PostProcessor(mock_workflow)


@pytest.fixture
def sample_state():
    """创建示例状态数据"""
    return {
        "workflow_id": "test_workflow_123",
        "characters": [
            {
                "name": "张三",
                "type": "主角",
                "description": "聪明勇敢的年轻人",
                "personality": "正直善良",
                "speech_style": "直接明了",
                "aliases": ["小张"],
            },
            {
                "name": "李四",
                "type": "配角",
                "description": "张三的朋友",
                "personality": "幽默风趣",
                "speech_style": "爱开玩笑",
                "aliases": [],
            },
            {
                "name": "王五",
                "type": "反派",
                "description": "无关紧要的角色",
                "personality": "奸诈",
                "speech_style": "阴险",
                "aliases": [],
            },
        ],
        "annotated_chapters": [
            {
                "title": "第一章",
                "content": "故事开始...",
                "dialogue_annotations": [
                    {
                        "type": "dialogue",
                        "speaker": "张三",
                        "emotion": "happy",
                        "content": "你好！",
                    },
                    {
                        "type": "dialogue",
                        "speaker": "李四",
                        "emotion": "uncertain",
                        "content": "嗯，你好...",
                    },
                    {
                        "type": "narration",
                        "speaker": "narrator",
                        "emotion": "neutral",
                        "content": "两人相视而笑。",
                    },
                ],
            },
            {
                "title": "第二章",
                "content": "故事继续...",
                "dialogue_annotations": [
                    {
                        "type": "dialogue",
                        "speaker": "张三",
                        "emotion": "excited",
                        "content": "太好了！",
                    }
                ],
            },
        ],
    }


def test_extract_characters_from_dialogues(post_processor, sample_state):
    """测试从对话中提取角色功能"""
    dialogue_characters = post_processor._extract_characters_from_dialogues(
        sample_state["annotated_chapters"]
    )

    assert "张三" in dialogue_characters
    assert "李四" in dialogue_characters
    assert "narrator" not in dialogue_characters  # 叙述者不应该被包含
    assert "unknown" not in dialogue_characters  # 未知角色不应该被包含


def test_refine_character_list(post_processor, sample_state):
    """测试角色列表优化功能"""
    dialogue_characters = ["张三", "李四"]

    refined_characters = post_processor._refine_character_list(
        sample_state["characters"], dialogue_characters
    )

    # 应该只保留在对话中出现的角色
    assert len(refined_characters) == 2
    character_names = [char["name"] for char in refined_characters]
    assert "张三" in character_names
    assert "李四" in character_names
    assert "王五" not in character_names  # 王五没有在对话中出现


def test_process_uncertain_emotions(post_processor, sample_state):
    """测试处理不确定情感功能"""
    processed_chapters = post_processor._process_uncertain_emotions(
        sample_state["annotated_chapters"], sample_state["characters"]
    )

    # 检查第一章的第二个对话（李四的"uncertain"情感）
    first_chapter = processed_chapters[0]
    second_dialogue = first_chapter["dialogue_annotations"][1]

    assert second_dialogue["speaker"] == "李四"
    assert second_dialogue["emotion"] != "uncertain"  # 应该被推断为其他情感
    assert second_dialogue.get("emotion_inferred") is True  # 应该标记为推断的


def test_infer_emotion(post_processor):
    """测试情感推断功能"""
    # 测试高兴的情感
    happy_annotation = {"content": "太好了！", "speaker": "张三"}
    emotion = post_processor._infer_emotion(happy_annotation, [])
    assert emotion == "excited"

    # 测试感谢的情感
    grateful_annotation = {"content": "谢谢你的帮助", "speaker": "李四"}
    emotion = post_processor._infer_emotion(grateful_annotation, [])
    assert emotion == "happy"

    # 测试道歉的情感
    apologetic_annotation = {"content": "对不起，我错了", "speaker": "王五"}
    emotion = post_processor._infer_emotion(apologetic_annotation, [])
    assert emotion == "apologetic"


def test_fuzzy_match_character(post_processor, sample_state):
    """测试角色模糊匹配功能"""
    # 测试别名匹配
    matched = post_processor._fuzzy_match_character("小张", sample_state["characters"])
    assert matched is not None
    assert matched["name"] == "张三"

    # 测试部分匹配
    matched = post_processor._fuzzy_match_character("张", sample_state["characters"])
    assert matched is not None
    assert matched["name"] == "张三"

    # 测试无匹配
    matched = post_processor._fuzzy_match_character("赵六", sample_state["characters"])
    assert matched is None


def test_post_process_integration(post_processor, sample_state):
    """测试后处理节点的整体功能"""
    result = post_processor.post_process(sample_state)

    # 检查返回结果的基本结构
    assert result["step"] == "quality_check"
    assert "characters" in result
    assert "annotated_chapters" in result
    assert "updated_at" in result
    assert result["processing_progress"] == 0.85

    # 检查角色过滤是否正确
    refined_characters = result["characters"]
    character_names = [char["name"] for char in refined_characters]
    assert "张三" in character_names
    assert "李四" in character_names
    # 王五没有在对话中出现，应该被过滤掉
    if len(character_names) < 3:
        assert "王五" not in character_names

    # 检查不确定情感是否被处理
    processed_chapters = result["annotated_chapters"]
    uncertain_emotions = []
    for chapter in processed_chapters:
        for annotation in chapter.get("dialogue_annotations", []):
            if annotation.get("emotion") == "uncertain":
                uncertain_emotions.append(annotation)

    # 不确定情感应该被处理掉
    assert len(uncertain_emotions) == 0

    # 检查是否正确保存了原始角色数据
    assert "original_characters" in result
    original_characters = result["original_characters"]
    assert len(original_characters) == 3  # 原始有3个角色
    original_character_names = [char["name"] for char in original_characters]
    assert "张三" in original_character_names
    assert "李四" in original_character_names
    assert "王五" in original_character_names  # 原始角色应该保持完整

    # 检查旁白角色是否被正确添加
    final_characters = result["characters"]
    final_character_names = [char["name"] for char in final_characters]
    assert "旁白" in final_character_names  # 应该包含旁白角色

    # 检查角色排序是否正确（旁白应该在第一位）
    assert final_characters[0]["name"] == "旁白"

    # 检查旁白角色的属性
    narrator = final_characters[0]
    assert narrator["type"] == "narrator"
    assert narrator["description"] == "小说的叙述者"
    assert narrator["source"] == "system_added"


def test_ensure_narrator_and_sort_characters(post_processor):
    """测试确保旁白角色并排序功能"""
    # 测试数据：没有旁白角色的角色列表
    characters_without_narrator = [
        {"name": "王五", "type": "反派", "description": "反派"},
        {"name": "张三", "type": "主角", "description": "主角"},
        {"name": "李四", "type": "配角", "description": "配角"},
        {"name": "赵六", "type": "unknown", "description": "未知角色"},
    ]

    result = post_processor._ensure_narrator_and_sort_characters(
        characters_without_narrator
    )

    # 检查旁白是否被添加
    assert len(result) == 5  # 原来4个 + 旁白1个
    character_names = [char["name"] for char in result]
    assert "旁白" in character_names

    # 检查排序是否正确：旁白 -> 主角 -> 配角 -> 其他
    expected_order = ["旁白", "张三", "李四", "王五", "赵六"]
    actual_order = [char["name"] for char in result]
    assert actual_order == expected_order


def test_ensure_narrator_when_already_exists(post_processor):
    """测试当旁白角色已存在时不会重复添加"""
    characters_with_narrator = [
        {"name": "旁白", "type": "narrator", "description": "现有旁白"},
        {"name": "张三", "type": "主角", "description": "主角"},
    ]

    result = post_processor._ensure_narrator_and_sort_characters(
        characters_with_narrator
    )

    # 检查旁白没有被重复添加
    assert len(result) == 2
    narrator_count = sum(1 for char in result if char["name"] == "旁白")
    assert narrator_count == 1

    # 检查旁白仍然在第一位
    assert result[0]["name"] == "旁白"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心模块
包含系统初始化、依赖注入等核心功能
"""

from .dependencies import (
    TTSManagerDep,
    get_tts_manager_dependency,
)
from .system_init import (
    SystemInitializer,
    get_system_initializer,
    get_tts_manager,
    initialize_system,
    is_system_initialized,
)

__all__ = [
    "SystemInitializer",
    "get_system_initializer",
    "initialize_system",
    "get_tts_manager",
    "is_system_initialized",
    "get_tts_manager_dependency",
    "TTSManagerDep",
]

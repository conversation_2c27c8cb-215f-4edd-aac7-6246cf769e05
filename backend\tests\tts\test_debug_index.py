#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# 添加 backend/src 到 Python 路径
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

# 设置环境变量
os.environ["INDEX_TTS_MODEL_PATH"] = "resources/TTS/index-tts/checkpoints"
os.environ["INDEX_TTS_CONFIG_PATH"] = "resources/TTS/index-tts/checkpoints/config.yaml"
os.environ["INDEX_TTS_DEVICE"] = "auto"

print("测试 IndexTTS 初始化过程...")

try:
    from tts.index.tts_impl import IndexTTS

    print("1. ✓ IndexTTS 类导入成功")

    # 直接创建实例（模拟 TTS 管理器中的第一次初始化）
    engine1 = IndexTTS()
    print("2. ✓ IndexTTS 实例创建成功")

    # 调用 initialize 方法（模拟 TTS 管理器中的测试初始化）
    result = engine1.initialize()
    print(f"3. IndexTTS initialize() 返回: {result}")

    if result:
        print("✓ IndexTTS 初始化完全成功")
    else:
        print("✗ IndexTTS 初始化失败")

except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback

    traceback.print_exc()

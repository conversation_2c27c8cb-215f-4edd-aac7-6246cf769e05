import {
  <PERSON>,
  Bot,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  MicVocal,
  Tally1,
  <PERSON>,
  Play,
  Pause,
  BarChart3,
  Shuffle,
  Save,
  User,
  UserCheck,
  BookOpen,
  UserCircle,
} from 'lucide-react';
import { useTTSStore } from '../store/ttsStore';
import { useAudioPlayer } from '../hooks/useAudioPlayer';

export default function CharacterConfigPanel() {
  const {
    settings,
    characters,
    updateSettings,
    updateCharacter,
    processNovel,
    isProcessingNovel,
    getVoicesByCurrentEngine,
    getCurrentEngine,
    engineError,
    isLoadingEngines,
  } = useTTSStore();

  // 根据角色信息返回合适的图标
  const getCharacterIcon = (character: any) => {
    if (character.name === '旁白') {
      return <BookOpen size={18} className="text-blue-600" />;
    }

    // 根据 emoji 或其他属性判断性别
    if (
      character.emoji === '👨' ||
      (character.gender && character.gender === '男')
    ) {
      return <User size={18} className="text-blue-500" />;
    }

    if (
      character.emoji === '👩' ||
      (character.gender && character.gender === '女')
    ) {
      return <UserCheck size={18} className="text-pink-500" />;
    }

    // 默认图标
    return <UserCircle size={18} className="text-gray-500" />;
  };

  // 获取当前引擎的声音列表
  const currentEngineVoices = getVoicesByCurrentEngine();
  const currentEngine = getCurrentEngine();

  // 处理重试
  const handleRetry = async () => {
    if (window.__ttsRefetch) {
      await window.__ttsRefetch();
    }
  };

  // 音频播放器
  const {
    playVoiceFile,
    isLoading: audioLoading,
    isPlayingFile,
  } = useAudioPlayer();

  const getVoiceDisplayName = (voiceId: string) => {
    const voice = currentEngineVoices.find((v) => v.id === voiceId);
    return voice
      ? `${voice.name} (${voice.gender === 'male' ? '男' : '女'})`
      : '';
  };

  const getVoiceDescription = (voiceId: string) => {
    const voice = currentEngineVoices.find((v) => v.id === voiceId);
    return voice ? voice.description : '';
  };

  const getVoiceStyles = (voiceId: string) => {
    const voice = currentEngineVoices.find((v) => v.id === voiceId);
    return voice?.styles || [];
  };

  // 获取声音文件路径
  const getVoicePath = (voiceId: string, style?: string) => {
    const voice = currentEngineVoices.find((v) => v.id === voiceId);
    if (!voice || !voice.stylesPaths) {
      return null;
    }

    const styleName = style || '通用';
    // 如果指定的样式不存在，尝试使用第一个可用样式
    if (voice.stylesPaths[styleName]) {
      return voice.stylesPaths[styleName];
    }

    // 如果没有找到指定样式，返回第一个可用样式
    const availableStyles = Object.keys(voice.stylesPaths);
    if (availableStyles.length > 0) {
      return voice.stylesPaths[availableStyles[0]];
    }

    return null;
  };

  // 处理试听按钮点击
  const handlePlayVoice = async (voiceId: string, style?: string) => {
    if (!voiceId) return;

    try {
      const voice = currentEngineVoices.find((v) => v.id === voiceId);
      const voicePath = getVoicePath(voiceId, style);
      if (!voicePath) {
        const styleName = style || '通用';
        console.error(
          `未找到声音文件路径 - 声音: ${voice?.name || voiceId}, 样式: ${styleName}`
        );
        console.error('可用样式:', voice?.styles || []);
        return;
      }
      await playVoiceFile(voicePath);
    } catch (error) {
      console.error('播放声音失败:', error);
    }
  };

  const handleModeChange = (mode: 'single' | 'multi-character') => {
    updateSettings({ mode });
  };

  const handleProcessNovel = async () => {
    try {
      await processNovel();
    } catch (error) {
      console.error('小说处理失败:', error);
    }
  };

  return (
    <div className="panel animate-in">
      <div className="panel-header p-[16px] pt-[8px] pb-[8px]">
        <h2 className="panel-title text-sm flex items-center">
          <MicVocal size={12} className="mr-1" />
          声音配置
        </h2>
        {currentEngine && !engineError && (
          <div className="text-[10px] text-neutral-500">
            {`共 ${currentEngineVoices.length} 个可用声音`}
          </div>
        )}
        {engineError && (
          <div className="text-[10px] text-warning-600">暂无可用声音</div>
        )}
      </div>

      <div className="panel-body space-y-3">
        {/* 错误提示 - 显示在顶部但不阻止界面 */}
        {engineError && (
          <div className="alert alert-warning">
            <div className="flex flex-col gap-2">
              <span className="text-xs">
                ⚠️ 后端服务连接失败，声音配置功能暂不可用
              </span>
              <button
                className="btn btn-sm btn-ghost"
                onClick={handleRetry}
                disabled={isLoadingEngines}
              >
                {isLoadingEngines ? (
                  <>
                    <span className="loading loading-spinner loading-xs"></span>
                    重试中...
                  </>
                ) : (
                  <span className="flex items-center">
                    <RefreshCw size={14} className="mr-1" />
                    重试连接
                  </span>
                )}
              </button>
            </div>
          </div>
        )}

        {/* 加载状态 */}
        {isLoadingEngines && (
          <div className="flex items-center justify-center py-4">
            <div className="loading loading-spinner loading-md"></div>
            <span className="ml-2">加载引擎数据...</span>
          </div>
        )}

        {/* TTS模式选择 */}
        <div className="-mt-3">
          <div className="flex border-b border-gray-200 -mx-[16px]">
            <button
              className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${
                settings.mode === 'single'
                  ? 'border-primary-500 text-primary-600 bg-primary-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => handleModeChange('single')}
            >
              <span className="flex items-center justify-center gap-2">
                <Tally1 size={14} /> <span>单一声音</span>
              </span>
            </button>
            <button
              className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${
                settings.mode === 'multi-character'
                  ? 'border-primary-500 text-primary-600 bg-primary-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => handleModeChange('multi-character')}
            >
              <span className="flex items-center justify-center gap-2">
                <Users size={14} /> <span>多角色声音</span>
              </span>
            </button>
          </div>
          {/* <div className="bg-gray-50 px-4 py-3 text-center" data-oid="d-51f01">
                                                            <p className="text-xs text-gray-500" data-oid=":utyolw">
                                                              {settings.mode === 'single'
                                                                ? '所有文本使用同一个声音'
                                                                : '为不同角色分配不同声音'}
                                                            </p>
                                                           </div> */}
        </div>

        {/* 单一声音模式 */}
        {settings.mode === 'single' && (
          <div className="space-y-4">
            <div>
              <label className="label">选择声音</label>
              <select
                value={settings.singleVoiceId || ''}
                onChange={(e) => {
                  const voiceId = e.target.value;
                  const voice = currentEngineVoices.find(
                    (v) => v.id === voiceId
                  );
                  // 设置默认样式：优先选择"通用"，如果没有则选择第一个可用样式
                  let defaultStyle = '通用';
                  if (voice?.styles) {
                    if (
                      !voice.styles.includes('通用') &&
                      voice.styles.length > 0
                    ) {
                      defaultStyle = voice.styles[0];
                    }
                  }
                  updateSettings({
                    singleVoiceId: voiceId,
                    singleVoiceStyle: defaultStyle,
                  });
                }}
                className="select w-full p-[8px] text-[16px]"
                disabled={
                  Boolean(engineError) ||
                  isLoadingEngines ||
                  currentEngineVoices.length === 0
                }
              >
                {currentEngineVoices.length === 0 ? (
                  <option value="" disabled>
                    {engineError ? '服务连接失败，请重试' : '暂无可用声音'}
                  </option>
                ) : (
                  <>
                    <option value="">请选择声音...</option>
                    {currentEngineVoices.map((voice) => (
                      <option key={voice.id} value={voice.id}>
                        {voice.name} ({voice.gender === 'male' ? '男' : '女'})
                      </option>
                    ))}
                  </>
                )}
              </select>
              {settings.singleVoiceId && (
                <div className="text-xs text-neutral-500 mt-1">
                  {getVoiceDescription(settings.singleVoiceId)}
                </div>
              )}
            </div>

            {/* 声音样式选择 */}
            {settings.singleVoiceId && (
              <div>
                <label className="label">选择声音样式</label>
                <select
                  value={(() => {
                    if (!settings.singleVoiceId) return '通用';
                    const styles = getVoiceStyles(settings.singleVoiceId);
                    const currentStyle = settings.singleVoiceStyle || '通用';
                    // 如果当前样式不在可用样式中，使用第一个可用样式
                    return styles.includes(currentStyle)
                      ? currentStyle
                      : styles.length > 0
                        ? styles[0]
                        : '通用';
                  })()}
                  onChange={(e) =>
                    updateSettings({ singleVoiceStyle: e.target.value })
                  }
                  className="select w-full p-[8px] text-[16px]"
                >
                  {getVoiceStyles(settings.singleVoiceId).map((style) => (
                    <option key={style} value={style}>
                      {style}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {settings.singleVoiceId && (
              <div className="bg-neutral-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-neutral-700">
                    已选择: {getVoiceDisplayName(settings.singleVoiceId)}
                    {settings.singleVoiceStyle && (
                      <div className="text-xs text-neutral-500 mt-1">
                        样式: {settings.singleVoiceStyle}
                      </div>
                    )}
                  </div>
                  <button
                    className="btn btn-ghost btn-xs"
                    title="试听声音"
                    disabled={
                      audioLoading ||
                      Boolean(engineError) ||
                      !settings.singleVoiceId
                    }
                    onClick={() =>
                      handlePlayVoice(
                        settings.singleVoiceId!,
                        settings.singleVoiceStyle
                      )
                    }
                  >
                    {audioLoading ? (
                      <span className="loading loading-spinner loading-xs"></span>
                    ) : (
                      (() => {
                        const voicePath = getVoicePath(
                          settings.singleVoiceId!,
                          settings.singleVoiceStyle
                        );
                        return voicePath && isPlayingFile(voicePath) ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        );
                      })()
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 多角色声音模式 */}
        {settings.mode === 'multi-character' && (
          <div className="space-y-4">
            {/* AI识别角色按钮 */}
            <div className="text-center">
              <button
                onClick={handleProcessNovel}
                disabled={isProcessingNovel}
                className="btn btn-sm w-full h-10 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg border-0 shadow-md hover:from-blue-600 hover:to-indigo-700 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/30 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 ease-out disabled:bg-gray-400 disabled:shadow-none disabled:scale-100 group"
              >
                {isProcessingNovel ? (
                  <div className="flex items-center justify-center">
                    <span className="loading loading-spinner loading-sm mr-2"></span>
                    <span>处理中...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Bot
                      size={18}
                      className="group-hover:animate-bounce group-hover:text-blue-100"
                    />

                    <span>AI识别角色与对话分割</span>
                  </div>
                )}
              </button>
            </div>

            {/* 角色统计 */}
            {characters.length > 0 && (
              <div className="bg-neutral-50 rounded-lg p-4">
                <div className="text-sm font-medium text-neutral-700 mb-2 flex items-center gap-2">
                  <BarChart3 size={16} />
                  角色统计
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-neutral-600">
                    识别角色:{' '}
                    <span className="font-medium">{characters.length}个</span>
                  </div>
                  <div className="text-neutral-600">
                    待配置:{' '}
                    <span className="font-medium text-orange-600">
                      {characters.filter((c) => !c.voiceId).length}个
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* 角色列表 */}
            <div className="space-y-4">
              <div className="text-xs font-medium text-neutral-700 flex items-center gap-2">
                <Users size={14} />
                <span>角色列表</span>
              </div>

              {characters.length === 0 ? (
                <div className="text-center py-8 text-neutral-500">
                  <div className="flex justify-center mb-2">
                    <Search size={48} className="text-neutral-400" />
                  </div>
                  <p className="text-xs">暂无识别到角色</p>
                  <p className="text-xs">请先点击"AI识别角色"</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-[800px] overflow-y-auto scrollbar-hide">
                  {characters.map((character, index) => (
                    <div
                      key={character.id}
                      className={`character-item ${character.voiceId ? 'character-item-active' : ''} scale-in p-3`}
                      style={{ animationDelay: `${index * 0.05}s` }}
                    >
                      {/* 角色信息 */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getCharacterIcon(character)}
                          <div>
                            <div className="font-medium text-neutral-800 text-xs">
                              {character.name}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="badge badge-primary text-[10px]">
                            {character.percentage}%
                          </div>
                        </div>
                      </div>

                      {/* 声音选择 */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <select
                            value={character.voiceId}
                            onChange={(e) => {
                              const voiceId = e.target.value;
                              const voice = currentEngineVoices.find(
                                (v) => v.id === voiceId
                              );
                              // 设置默认样式：优先选择"通用"，如果没有则选择第一个可用样式
                              let defaultStyle = '通用';
                              if (voice?.styles) {
                                if (
                                  !voice.styles.includes('通用') &&
                                  voice.styles.length > 0
                                ) {
                                  defaultStyle = voice.styles[0];
                                }
                              }
                              updateCharacter(character.id, {
                                voiceId: voiceId,
                                voiceStyle: defaultStyle,
                              });
                            }}
                            className="select flex-1 text-xs select-sm"
                            title={
                              character.voiceId
                                ? getVoiceDescription(character.voiceId)
                                : ''
                            }
                            disabled={
                              Boolean(engineError) ||
                              isLoadingEngines ||
                              currentEngineVoices.length === 0
                            }
                          >
                            {currentEngineVoices.length === 0 ? (
                              <option value="" disabled>
                                {engineError ? '服务连接失败' : '暂无可用声音'}
                              </option>
                            ) : (
                              <>
                                <option value="">请选择声音...</option>
                                {currentEngineVoices.map((voice) => (
                                  <option key={voice.id} value={voice.id}>
                                    {voice.name} (
                                    {voice.gender === 'male' ? '男' : '女'})
                                  </option>
                                ))}
                              </>
                            )}
                          </select>

                          <button
                            title="试听声音"
                            className="btn btn-ghost btn-xs px-2"
                            disabled={
                              !character.voiceId ||
                              audioLoading ||
                              Boolean(engineError)
                            }
                            onClick={() =>
                              handlePlayVoice(
                                character.voiceId,
                                character.voiceStyle
                              )
                            }
                          >
                            {audioLoading && character.voiceId ? (
                              <span className="loading loading-spinner loading-xs"></span>
                            ) : (
                              (() => {
                                const voicePath = getVoicePath(
                                  character.voiceId,
                                  character.voiceStyle
                                );
                                return character.voiceId &&
                                  voicePath &&
                                  isPlayingFile(voicePath) ? (
                                  <Pause className="w-4 h-4" />
                                ) : (
                                  <Play className="w-4 h-4" />
                                );
                              })()
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            {characters.length > 0 && (
              <div className="flex gap-3 pt-4 border-t border-neutral-200">
                <button className="btn bg-secondary hover:bg-blue-600 text-white border-blue-500 hover:border-blue-600 flex-1 flex items-center justify-center gap-2 group">
                  <Shuffle size={16} className="group-hover:animate-ping" />
                  智能分配
                </button>
                <button className="btn bg-primary hover:bg-primary-600 text-white border-primary hover:border-primary-600 flex-1 flex items-center justify-center gap-2 group">
                  <Save size={16} className="group-hover:animate-bounce" />
                  保存配置
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的环境变量测试
"""

import os
from pathlib import Path

from dotenv import load_dotenv


def test_env_simple():
    """简单测试环境变量加载"""
    print("=== 简化环境变量测试 ===")

    # 1. 检查初始状态
    print(f"1. 初始 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")

    # 2. 手动加载 .env 文件
    env_file = Path(".env")
    print(f"2. .env 文件存在: {env_file.exists()}")

    if env_file.exists():
        print("3. 读取 .env 文件内容:")
        with open(env_file, "r", encoding="utf-8") as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if "INDEX_TTS_MODEL_PATH" in line:
                    print(f"   第{i}行: {line.strip()}")

        print("4. 加载 .env 文件...")
        load_dotenv(env_file)
        print(f"   加载后 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")

    # 3. 检查所有包含 INDEX_TTS 的环境变量
    print("5. 所有 INDEX_TTS 相关环境变量:")
    for key, value in os.environ.items():
        if "INDEX_TTS" in key:
            print(f"   {key} = {value}")


if __name__ == "__main__":
    test_env_simple()

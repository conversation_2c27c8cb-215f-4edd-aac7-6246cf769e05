#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parrot Novels 后端启动脚本
提供便捷的服务启动方式
"""

import argparse
import os
import sys
from pathlib import Path

import uvicorn
from src.utils.logging_config import get_logger

logger = get_logger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动 Parrot Novels 后端服务")

    parser.add_argument(
        "--host", default="127.0.0.1", help="服务器绑定地址 (默认: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", type=int, default=8000, help="服务器端口 (默认: 8000)"
    )
    parser.add_argument("--reload", action="store_true", help="启用自动重载 (开发模式)")
    parser.add_argument(
        "--workers", type=int, default=1, help="工作进程数量 (生产模式)"
    )
    parser.add_argument(
        "--log-level",
        default="info",
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help="日志级别",
    )
    parser.add_argument("--env-file", default=".env", help="环境变量文件路径")
    parser.add_argument("--check", action="store_true", help="只检查配置，不启动服务")

    args = parser.parse_args()

    # 检查环境变量文件
    # 如果是相对路径，先在当前目录查找，再在项目根目录查找
    env_file = Path(args.env_file)
    if not env_file.is_absolute():
        # 先检查当前目录
        if not env_file.exists():
            # 再检查项目根目录（backend的上一级目录）
            project_root = Path(__file__).parent.parent
            env_file_in_root = project_root / args.env_file
            if env_file_in_root.exists():
                env_file = env_file_in_root
                logger.info(f"找到环境变量文件: {env_file.absolute()}")
            else:
                logger.warning(f"警告: 环境变量文件 {args.env_file} 不存在")
                logger.info(f"已检查路径: {Path(args.env_file).absolute()}")
                logger.info(f"已检查路径: {env_file_in_root.absolute()}")
                logger.warning("请复制 .env.example 为 .env 并配置相关参数")
                if not args.check:
                    response = input("是否继续启动? (y/N): ")
                    if response.lower() != "y":
                        sys.exit(1)
        else:
            logger.info(f"找到环境变量文件: {env_file.absolute()}")
    else:
        # 绝对路径直接检查
        if not env_file.exists():
            logger.warning(f"警告: 环境变量文件 {env_file} 不存在")
            logger.warning("请复制 .env.example 为 .env 并配置相关参数")
            if not args.check:
                response = input("是否继续启动? (y/N): ")
                if response.lower() != "y":
                    sys.exit(1)

    # 设置环境变量
    os.environ.setdefault("LOG_LEVEL", args.log_level.upper())

    if args.check:
        logger.info("配置检查模式")
        logger.info(f"主机: {args.host}")
        logger.info(f"端口: {args.port}")
        logger.info(f"重载: {args.reload}")
        logger.info(f"工作进程: {args.workers}")
        logger.info(f"日志级别: {args.log_level}")
        logger.info(f"环境文件: {env_file.absolute()}")

        # 尝试导入主应用检查配置
        try:
            logger.info("✓ 应用导入成功")

            # 检查系统初始化
            from src.core.system_init import initialize_system

            success = initialize_system()
            if success:
                logger.info("✓ 系统初始化成功")
            else:
                logger.error("✗ 系统初始化失败")
                sys.exit(1)

        except Exception as e:
            logger.error(f"✗ 配置检查失败: {e}")
            sys.exit(1)

        logger.info("配置检查完成")
        return

    # 启动服务
    logger.info("启动 Parrot Novels 后端服务...")
    logger.info(f"地址: http://{args.host}:{args.port}")
    logger.info(f"API文档: http://{args.host}:{args.port}/docs")
    logger.info(f"健康检查: http://{args.host}:{args.port}/health")

    # 配置uvicorn参数
    config = {
        "app": "main:app",
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level,
    }

    if args.reload:
        config["reload"] = True
        logger.info("开发模式: 自动重载已启用")
    else:
        config["workers"] = args.workers
        if args.workers > 1:
            logger.info(f"生产模式: 使用 {args.workers} 个工作进程")

    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        logger.info("\n服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

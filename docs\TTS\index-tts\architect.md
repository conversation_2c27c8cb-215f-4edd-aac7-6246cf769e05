IndexTTS 使用了三个主要的深度学习模型组件来实现端到端的文本到语音合成。

## 主要模型架构

IndexTTS 采用了基于 GPT 的架构，包含以下三个核心模型：

### 1. GPT-based 声学模型 (UnifiedVoice)

这是系统的核心模型，负责将文本转换为声学特征。 [1](#1-0) 

该模型基于 GPT-2 架构实现： [2](#1-1) 

GPT 模型的配置参数包括： [3](#1-2) 

### 2. BigVGAN 声码器

BigVGAN 是负责将声学特征转换为最终音频波形的生成对抗网络模型。 [4](#1-3) 

该模型在推理时被加载： [5](#1-4) 

BigVGAN 的配置参数： [6](#1-5) 

### 3. 条件编码器 (Conformer Encoder)

用于处理参考音频的条件编码器，支持声音克隆功能： [7](#1-6) 

## 模型文件

系统需要加载以下预训练模型文件： [8](#1-7) 

- `gpt.pth`: GPT 声学模型权重
- `bigvgan_generator.pth`: BigVGAN 生成器权重  
- `dvae.pth`: 离散变分自编码器权重（用于特征量化）

## 推理流程

在推理过程中，这些模型按以下顺序协作： [9](#1-8) 

然后通过 BigVGAN 生成最终音频： [10](#1-9) 

## Notes

IndexTTS 的架构受到 XTTS 和 Tortoise-TTS 的启发，但在多个模块上进行了改进，包括使用 Conformer 条件编码器和集成 BigVGAN2 来优化音频质量。系统支持中文字符-拼音混合建模，可以快速纠正发音错误。

Wiki pages you might want to explore:
- [BigVGAN Vocoder (index-tts/index-tts)](/wiki/index-tts/index-tts#4.4)
#!/usr/bin/env python3
import logging
import os
import sys
from pathlib import Path

# 设置环境变量
os.environ["INDEX_TTS_MODEL_PATH"] = "resources/TTS/index-tts/checkpoints"
os.environ["INDEX_TTS_CONFIG_PATH"] = "resources/TTS/index-tts/checkpoints/config.yaml"
os.environ["INDEX_TTS_DEVICE"] = "auto"
os.environ["TTS_ENABLED_ENGINES"] = "edge,index,minimax"

# 添加 backend/src 到路径
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

print("=== 调试 TTS 管理器初始化 ===")
print(f"sys.argv: {sys.argv}")

try:
    # 导入并创建 TTS 管理器
    from tts.manager import create_tts_manager

    print("正在创建 TTS 管理器...")
    manager = create_tts_manager()

    print(f"可用引擎: {manager.list_available_engines()}")

    # 测试 IndexTTS
    if "index" in manager.list_available_engines():
        print("IndexTTS 可用，测试创建实例...")
        index_engine = manager.get_engine("index")
        print("IndexTTS 实例创建成功")
    else:
        print("IndexTTS 不可用")

except Exception as e:
    print(f"错误: {e}")
    import traceback

    traceback.print_exc()


```mermaid
graph TD
    A["小说内容输入"] --> B["split_chapters<br/>章节分割"]
    B --> C["validate_input<br/>输入验证"]
    C --> D["analyze_characters<br/>角色分析"]
    D --> E["annotate_dialogues<br/>对话标注"]
    E --> F["quality_check<br/>质量检查"]
    F --> G{"质量分数检查"}
    G -->|"分数过低"| H["retry_processing<br/>重试处理"]
    G -->|"分数达标"| I["finalize_results<br/>完成处理"]
    G -->|"失败"| J["END<br/>流程结束"]
    H --> E
    I --> J
    
    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#fce4ec
```
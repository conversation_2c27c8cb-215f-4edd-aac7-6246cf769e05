#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS 实现
基于 index-tts 项目的 TTS 引擎实现
"""

import logging
import os
import sys
import tempfile
from pathlib import Path
from typing import Any, Dict, Iterator, Optional

from ..engine import TTSEngineBase

try:
    from src.schemas.tts_schema import TTSVoice
    from src.services.voice_service import VoiceService
    from src.utils.path_utils import resolve_resource_path
except ImportError:
    pass


def _get_bool_env(key: str, default: bool = False) -> bool:
    """获取布尔类型环境变量"""
    value = os.getenv(key, str(default)).lower()
    return value in ("true", "1", "yes", "on")


def _get_env_config() -> Dict[str, Any]:
    """从环境变量获取配置"""
    return {
        "model_path": os.getenv(
            "INDEX_TTS_MODEL_PATH", "resources/TTS/index-tts/checkpoints"
        ),
        "config_path": os.getenv(
            "INDEX_TTS_CONFIG_PATH", "resources/TTS/index-tts/checkpoints/config.yaml"
        ),
        "precision": os.getenv("INDEX_TTS_PRECISION", "fp16"),
        "device": os.getenv("INDEX_TTS_DEVICE", "cpu"),
        "use_cuda_kernel": _get_bool_env("INDEX_TTS_USE_CUDA_KERNEL", False),
        "reference_voice": os.getenv("INDEX_TTS_REFERENCE_VOICE") or None,
        "fast_startup": _get_bool_env("FAST_STARTUP", False),
    }


class IndexTTS(TTSEngineBase):
    """IndexTTS 引擎实现

    基于 index-tts 项目的零样本语音合成引擎
    支持中英文混合语音合成
    """

    def initialize(self) -> bool:
        """初始化 IndexTTS 引擎

        Returns:
            bool: True 表示初始化成功，False 表示初始化失败
        """
        # 如果已经初始化过，直接返回结果
        if self._engine_initialized:
            return self.tts_engine is not None

        try:
            self._initialize_engine()
            self._engine_initialized = True
            return True
        except Exception as e:
            self.logger.error(f"IndexTTS 引擎初始化失败: {e}")
            self._engine_initialized = True  # 标记已尝试初始化，避免重复尝试
            return False

    def get_configurable_parameters(self) -> Dict[str, Dict[str, Any]]:
        """获取IndexTTS可配置的参数信息

        Returns:
            参数配置字典，包含推理相关参数和语音参数
        """
        return {
            "enable_fast_inference": {
                "type": "bool",
                "control": "switch",
                "label": "快速推理",
                "default": False,
                "description": "开启快速推理模式，可以提升2-10倍速度，适合长文本合成",
                "category": "performance",
                "advanced": False,
            },
            "max_text_tokens_per_sentence": {
                "type": "int",
                "control": "slider",
                "label": "分句Token数",
                "range": [30, 300],
                "default": 100,
                "description": "每个句子的最大token数，较大值适合长句子，较小值适合短句子",
                "step": 10,
                "category": "performance",
                "advanced": True,
                "depends_on": {"enable_fast_inference": True},
            },
            "sentences_bucket_max_size": {
                "type": "int",
                "control": "slider",
                "label": "分桶大小",
                "range": [1, 10],
                "default": 4,
                "description": "句子分桶的最大容量，用于批量处理优化，较大值可能提升速度但占用更多内存",
                "step": 1,
                "category": "performance",
                "advanced": True,
                "depends_on": {"enable_fast_inference": True},
            },
        }

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化 IndexTTS 引擎

        Args:
            config: 配置字典，包含以下可选参数：
                - model_dir: 模型目录路径，默认为 "checkpoints"
                - cfg_path: 配置文件路径，默认为 "checkpoints/config.yaml"
                - is_fp16: 是否使用 FP16，默认为 True
                - device: 设备类型，默认为 None（自动选择）
                - use_cuda_kernel: 是否使用 CUDA 内核，默认为 None
                - reference_voice: 默认参考语音文件路径
        """
        # 延迟加载环境变量，确保在系统初始化后读取
        if config is None:
            config = _get_env_config()
        super().__init__(config)

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 兼容新旧配置字段名
        model_path = self.config.get(
            "model_path", self.config.get("model_dir", "checkpoints")
        )
        config_path = self.config.get(
            "config_path", self.config.get("cfg_path", "checkpoints/config.yaml")
        )

        # 将相对路径转换为基于项目根目录的绝对路径
        from ...utils.path_utils import resolve_path

        self.model_dir = (
            str(resolve_path(model_path, base_dir="project"))
            if not Path(model_path).is_absolute()
            else model_path
        )
        self.cfg_path = (
            str(resolve_path(config_path, base_dir="project"))
            if not Path(config_path).is_absolute()
            else config_path
        )

        # 处理精度配置
        precision = self.config.get("precision", "fp16")
        self.is_fp16 = (
            precision == "fp16" if precision else self.config.get("is_fp16", True)
        )

        self.device = self._resolve_device(self.config.get("device", None))
        self.use_cuda_kernel = self.config.get("use_cuda_kernel", None)
        self.reference_voice = self.config.get("reference_voice", None)
        self.fast_startup = self.config.get("fast_startup", False)

        # 初始化 TTS 引擎实例为 None，延迟初始化
        self.tts_engine = None
        self._engine_initialized = False

        # 初始化 voice_service
        try:
            self.voice_service = VoiceService()
        except Exception as e:
            self.logger.warning(f"初始化 VoiceService 失败: {e}")
            self.voice_service = None

    def _initialize_engine(self):
        """初始化 IndexTTS 引擎"""
        # 检查是否启用快速启动模式
        if self.fast_startup:
            self.logger.info("检测到快速启动模式，跳过 IndexTTS 引擎初始化")
            self.tts_engine = None
            return

        # 检查是否在测试环境中
        if self._is_test_environment():
            self.logger.info("检测到测试环境，跳过真实引擎初始化")
            self.tts_engine = None
            return

        try:
            # 尝试导入 IndexTTS
            from indextts.infer import IndexTTS as IndexTTSEngine

            # 显示解析后的路径
            self.logger.info(f"IndexTTS 模型目录: {self.model_dir}")
            self.logger.info(f"IndexTTS 配置文件: {self.cfg_path}")

            # 检查模型文件是否存在
            if not os.path.exists(self.model_dir):
                raise FileNotFoundError(f"模型目录不存在: {self.model_dir}")

            if not os.path.exists(self.cfg_path):
                raise FileNotFoundError(f"配置文件不存在: {self.cfg_path}")

            # 初始化引擎
            self.tts_engine = IndexTTSEngine(
                cfg_path=self.cfg_path,
                model_dir=self.model_dir,
                is_fp16=self.is_fp16,
                device=self.device,
                use_cuda_kernel=self.use_cuda_kernel,
            )

            self.logger.info("IndexTTS 引擎初始化成功")

        except ImportError as e:
            self.logger.error(f"无法导入 IndexTTS: {e}")
            self.logger.error("请确保已安装 index-tts 包")
            raise
        except Exception as e:
            self.logger.error(f"IndexTTS 引擎初始化失败: {e}")
            raise

    def _resolve_device(self, device: Optional[str]) -> Optional[str]:
        """解析设备类型

        Args:
            device: 设备配置，可以是 'auto', 'cpu', 'cuda' 等

        Returns:
            str: 解析后的设备类型
        """
        if device == "auto":
            # 自动检测设备
            try:
                import torch

                if torch.cuda.is_available():
                    device = "cuda"
                    self.logger.info("自动检测到CUDA设备，使用GPU")
                else:
                    device = "cpu"
                    self.logger.info("未检测到CUDA设备，使用CPU")
            except ImportError:
                device = "cpu"
                self.logger.info("PyTorch未安装，使用CPU")
        elif device is None:
            device = "cpu"

        self.logger.info(f"IndexTTS 使用设备: {device}")
        return device

    def _is_test_environment(self) -> bool:
        """检测是否在测试环境中"""
        # 检查是否有 pytest 在运行
        if "pytest" in sys.modules:
            self.logger.debug("检测到 pytest 模块")
            return True

        # 检查是否在 pytest 运行器中（通过环境变量）
        if any(key.startswith("PYTEST_") for key in os.environ):
            self.logger.debug("检测到 pytest 环境变量")
            return True

        # 检查是否通过 pytest 命令启动
        if len(sys.argv) > 0:
            main_script = Path(sys.argv[0]).name
            self.logger.debug(f"检查脚本名称: {main_script}")
            if main_script.startswith(("pytest", "py.test")) or main_script.endswith(
                "_test.py"
            ):
                self.logger.debug(f"脚本 {main_script} 被识别为测试脚本")
                return True

        self.logger.debug("未检测到测试环境")
        return False

    def _get_reference_voice_path(
        self, voice_id_name: str, voice_style_name: str
    ) -> str:
        """通过 voice_service 获取参考语音文件路径

        Args:
            voice_id_name: 语音ID名称
            voice_style_name: 语音风格名称

        Returns:
            str: 参考语音文件的完整路径

        Raises:
            ValueError: 当无法找到语音文件时
        """
        if self.voice_service:
            try:
                from ..engine import TTSEngineType

                # 从数据库查找语音信息
                voice_info = self.voice_service.get_voice_by_id_and_engine(
                    voice_id_name, TTSEngineType.INDEX_TTS
                )

                if (
                    voice_info
                    and voice_info.styles
                    and voice_style_name in voice_info.styles
                ):
                    # 构建完整的语音文件路径
                    voice_file_path = f"TTS/{voice_info.styles[voice_style_name]}"
                    reference_voice = str(resolve_resource_path(voice_file_path))
                    self.logger.info(f"找到语音文件: {reference_voice}")
                    return reference_voice
                else:
                    raise ValueError(f"未找到语音信息: {voice_id_name}")
            except Exception as e:
                self.logger.error(f"获取语音信息失败: {e}")
                # 回退到原始方式
                return voice_id_name
        else:
            # 如果 voice_service 不可用，回退到原始方式
            self.logger.warning(
                "VoiceService 不可用，使用传入的 voice_id_name 作为文件路径"
            )
            return voice_id_name

    def synthesize(
        self,
        text: str,
        voice_id_name: str,
        voice_style_name: str,
        output_path: Optional[str] = None,
        enable_fast_inference: bool = False,
        max_text_tokens_per_sentence: int = 100,
        sentences_bucket_max_size: int = 4,
        **kwargs,
    ) -> bytes:
        """合成语音

        Args:
            text: 要合成的文本
            voice_id_name: 语音ID或名称（必须）
            voice_style_name: 语音风格名称（必须）
            output_path: 输出文件路径（可选）
            enable_fast_inference: 是否开启快速推理模式，可以提升2-10倍速度，适合长文本
            max_text_tokens_per_sentence: 分句的最大token数，用于控制句子长度，范围30-300
            sentences_bucket_max_size: 分句分桶的最大容量，用于批量处理优化，范围1-10
            **kwargs: 其他参数
                - reference_voice: 参考语音文件路径
                - voice: reference_voice 的别名

        Returns:
            音频数据（bytes）
        """
        if not text or not text.strip():
            raise ValueError("文本不能为空")

        # 在快速启动模式下返回模拟数据
        if self.fast_startup:
            self.logger.info("快速启动模式：返回模拟音频数据")
            mock_data = f"mock_audio_data_for_{text[:20]}".encode("utf-8")

            # 如果指定了输出路径，保存模拟数据
            if output_path:
                self.save_audio(mock_data, output_path)

            return mock_data

        # 确保引擎已初始化
        if not self._engine_initialized:
            if not self.initialize():
                raise RuntimeError("IndexTTS 引擎初始化失败")

        if self.tts_engine is None:
            raise RuntimeError("IndexTTS 引擎未正确初始化")

        # 通过 voice_service 获取参考语音文件路径
        reference_voice = self._get_reference_voice_path(
            voice_id_name, voice_style_name
        )

        if not reference_voice:
            raise ValueError("必须提供参考语音文件")

        # 验证参考语音文件
        if not self.validate_reference_voice(reference_voice):
            raise ValueError(f"无效的参考语音文件: {reference_voice}")

        # 创建临时输出文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_output_path = temp_file.name

        try:
            # 根据快速推理参数选择合成方法
            if enable_fast_inference:
                self.logger.info(
                    f"使用快速推理模式，最大token数: {max_text_tokens_per_sentence}, 分桶大小: {sentences_bucket_max_size}"
                )
                # 检查 IndexTTS 是否支持 infer_fast 方法的参数
                if hasattr(self.tts_engine, "infer_fast"):
                    try:
                        # 尝试使用带参数的快速推理（如果支持的话）
                        self.tts_engine.infer_fast(
                            reference_voice,
                            text,
                            temp_output_path,
                            max_text_tokens_per_sentence=max_text_tokens_per_sentence,
                            sentences_bucket_max_size=sentences_bucket_max_size,
                        )
                    except TypeError:
                        # 如果不支持参数，使用默认的快速推理
                        self.logger.warning(
                            "IndexTTS版本不支持快速推理参数，使用默认快速推理"
                        )
                        self.tts_engine.infer_fast(
                            reference_voice, text, temp_output_path
                        )
                else:
                    self.logger.warning("IndexTTS版本不支持快速推理，使用标准推理")
                    self.tts_engine.infer(reference_voice, text, temp_output_path)
            else:
                # 使用标准推理
                self.logger.info("使用标准推理模式")
                self.tts_engine.infer(reference_voice, text, temp_output_path)

            # 读取生成的音频数据
            with open(temp_output_path, "rb") as f:
                audio_data = f.read()

            # 如果指定了输出路径，保存文件
            if output_path:
                self.save_audio(audio_data, output_path)

            return audio_data

        except Exception as e:
            self.logger.error(f"语音合成失败: {e}")
            raise
        finally:
            # 清理临时文件
            if os.path.exists(temp_output_path):
                os.unlink(temp_output_path)

    def synthesize_stream(
        self,
        text: str,
        voice_id_name: str,
        voice_style_name: str,
        enable_fast_inference: bool = False,
        max_text_tokens_per_sentence: int = 100,
        sentences_bucket_max_size: int = 4,
        **kwargs,
    ) -> Iterator[bytes]:
        """流式合成语音

        Args:
            text: 要合成的文本
            voice_id_name: 语音ID或名称（必须）
            voice_style_name: 语音风格名称（必须）
            enable_fast_inference: 是否开启快速推理模式，可以提升2-10倍速度，适合长文本
            max_text_tokens_per_sentence: 分句的最大token数，用于控制句子长度，范围30-300
            sentences_bucket_max_size: 分句分桶的最大容量，用于批量处理优化，范围1-10
            **kwargs: 其他参数
                - reference_voice: 参考语音文件路径
                - voice: reference_voice 的别名
                - chunk_size: 音频块大小，默认 1024

        Yields:
            音频数据块（bytes）
        """
        # 在快速启动模式或测试环境中返回模拟数据
        if self.fast_startup or self._is_test_environment():
            chunk_size = kwargs.get("chunk_size", 1024)
            mock_data = f"mock_stream_data_for_{text[:20]}".encode("utf-8")
            for i in range(0, len(mock_data), chunk_size):
                yield mock_data[i : i + chunk_size]
            return

        # 确保引擎已初始化
        if not self._engine_initialized:
            if not self.initialize():
                raise RuntimeError("IndexTTS 引擎初始化失败")

        # IndexTTS 不直接支持流式合成，这里模拟实现
        # 先完整合成，然后分块返回
        audio_data = self.synthesize(
            text,
            voice_id_name,
            voice_style_name,
            enable_fast_inference=enable_fast_inference,
            max_text_tokens_per_sentence=max_text_tokens_per_sentence,
            sentences_bucket_max_size=sentences_bucket_max_size,
            **kwargs,
        )

        chunk_size = kwargs.get("chunk_size", 1024)
        for i in range(0, len(audio_data), chunk_size):
            yield audio_data[i : i + chunk_size]

    def set_reference_voice(self, voice_path: str):
        """设置默认参考语音

        Args:
            voice_path: 参考语音文件路径
        """
        if not os.path.exists(voice_path):
            raise FileNotFoundError(f"参考语音文件不存在: {voice_path}")

        self.reference_voice = voice_path
        self.logger.info(f"设置默认参考语音: {voice_path}")

    def validate_reference_voice(self, voice_path: str) -> bool:
        """验证参考语音文件

        Args:
            voice_path: 参考语音文件路径

        Returns:
            是否有效
        """
        if not os.path.exists(voice_path):
            return False

        # 检查文件扩展名
        valid_extensions = [".wav", ".mp3", ".flac", ".m4a"]
        file_ext = Path(voice_path).suffix.lower()

        return file_ext in valid_extensions

    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息

        Returns:
            引擎信息字典
        """
        return {
            "name": "IndexTTS",
            "version": "1.0.0",
            "description": "零样本语音合成引擎，支持中英文混合",
            "models": ["IndexTTS-1.5"],
            "supported_languages": ["中", "英"],
            "type": "zero-shot",
            "supports_streaming": False,  # 不支持真正的流式合成
            "supports_fast_inference": True,  # 支持快速推理模式
        }

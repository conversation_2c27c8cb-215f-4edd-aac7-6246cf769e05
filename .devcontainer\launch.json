{"version": "0.2.0", "configurations": [{"name": "Python: FastAPI Backend", "type": "python", "request": "launch", "program": "/workspace/backend/main.py", "args": [], "console": "integratedTerminal", "cwd": "/workspace/backend", "env": {"PYTHONPATH": "/workspace/backend/src", "API_HOST": "0.0.0.0", "API_PORT": "8000", "DEBUG": "true"}, "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false, "stopOnEntry": false, "autoReload": {"enable": true}}, {"name": "Python: Backend Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["tests/", "-v", "--tb=short"], "console": "integratedTerminal", "cwd": "/workspace/backend", "env": {"PYTHONPATH": "/workspace/backend/src"}, "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false}, {"name": "Python: Text Service", "type": "python", "request": "launch", "program": "/workspace/backend/src/services/text_service.py", "args": [], "console": "integratedTerminal", "cwd": "/workspace/backend", "env": {"PYTHONPATH": "/workspace/backend/src"}, "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false}, {"name": "Python: TTS Service", "type": "python", "request": "launch", "program": "/workspace/backend/src/services/tts_service.py", "args": [], "console": "integratedTerminal", "cwd": "/workspace/backend", "env": {"PYTHONPATH": "/workspace/backend/src"}, "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false}, {"name": "Python: LangGraph Workflow", "type": "python", "request": "launch", "program": "/opt/conda/envs/parrot/bin/langgraph", "args": ["dev", "--port", "2024", "--host", "0.0.0.0"], "console": "integratedTerminal", "cwd": "/workspace", "env": {"PYTHONPATH": "/workspace/backend/src"}, "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false}, {"name": "Node.js: Frontend Dev Server", "type": "node", "request": "launch", "program": "/workspace/frontend/node_modules/.bin/vite", "args": ["--host", "0.0.0.0", "--port", "5173"], "console": "integratedTerminal", "cwd": "/workspace/frontend", "env": {"NODE_ENV": "development"}, "stopOnEntry": false, "skipFiles": ["<node_internals>/**"]}, {"name": "Attach to Python Process", "type": "python", "request": "attach", "processId": "${command:pickProcess}", "python": "/opt/conda/envs/parrot/bin/python", "justMyCode": false}, {"name": "Attach to Node.js Process", "type": "node", "request": "attach", "processId": "${command:pickProcess}", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Launch Full Stack", "configurations": ["Python: FastAPI Backend", "Node.js: Frontend Dev Server"], "stopAll": true}]}
"""
文本处理模块
负责文本的智能切割、格式化等处理功能
"""

import re
from typing import List


class TextProcessor:
    """文本处理器类"""

    def __init__(self):
        # 定义句子结束标点符号
        self.sentence_endings = r"[。！？；.!?;]"
        # 定义段落分隔符
        self.paragraph_separators = r"[\n\r\f\v]+"
        # 定义引号等成对标点
        self.quote_pairs = [
            ('"', '"'),
            ('"', '"'),
            ("「", "」"),
            ("『", "』"),
            ("(", ")"),
            ("（", "）"),
            ("[", "]"),
            ("【", "】"),
        ]

    def smart_split_text(
        self, text: str, max_length: int = 500, min_length: int = 50
    ) -> List[str]:
        """
        智能切割文本，不会在句子中间切断

        Args:
            text: 待切割的文本
            max_length: 最大切割长度
            min_length: 最小切割长度

        Returns:
            切割后的文本片段列表
        """
        if not text or not text.strip():
            return []

        # 预处理：统一换行符
        text = text.replace("\r\n", "\n").replace("\r", "\n")

        # 首先按段落分割
        paragraphs = re.split(self.paragraph_separators, text)

        result = []
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果当前段落加上现有chunk不会超过最大长度，直接添加
            if len(current_chunk + paragraph) <= max_length:
                if current_chunk:
                    current_chunk += "\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                # 保存当前chunk（如果有内容）
                if current_chunk.strip():
                    result.append(current_chunk.strip())

                # 处理当前段落
                if len(paragraph) <= max_length:
                    current_chunk = paragraph
                else:
                    # 段落太长，需要进一步切割
                    chunks = self._split_long_paragraph(
                        paragraph, max_length, min_length
                    )
                    if chunks:
                        # 添加除最后一个之外的所有chunk
                        result.extend(chunks[:-1])
                        current_chunk = chunks[-1]
                    else:
                        current_chunk = ""

        # 添加最后的chunk
        if current_chunk.strip():
            result.append(current_chunk.strip())

        return result

    def _split_long_paragraph(
        self, paragraph: str, max_length: int, min_length: int
    ) -> List[str]:
        """
        切割长段落

        Args:
            paragraph: 段落文本
            max_length: 最大长度
            min_length: 最小长度

        Returns:
            切割后的文本片段列表
        """
        # 先按句子分割
        sentences = self._split_into_sentences(paragraph)

        result = []
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 如果单个句子就超过最大长度，强制切割
            if len(sentence) > max_length:
                if current_chunk.strip():
                    result.append(current_chunk.strip())
                    current_chunk = ""

                # 强制按字数切割超长句子
                force_chunks = self._force_split_sentence(sentence, max_length)
                result.extend(force_chunks[:-1])
                current_chunk = force_chunks[-1] if force_chunks else ""
                continue

            # 检查添加当前句子是否会超过最大长度
            test_chunk = (
                current_chunk + sentence
                if not current_chunk
                else current_chunk + " " + sentence
            )

            if len(test_chunk) <= max_length:
                current_chunk = test_chunk
            else:
                # 如果当前chunk达到最小长度要求，保存它
                if len(current_chunk) >= min_length:
                    result.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # 当前chunk太短，强制添加当前句子
                    current_chunk = test_chunk

        # 添加最后的chunk
        if current_chunk.strip():
            result.append(current_chunk.strip())

        return result

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        将文本按句子分割

        Args:
            text: 输入文本

        Returns:
            句子列表
        """
        # 使用正则表达式查找句子结束标点
        sentences = []
        current_pos = 0

        for match in re.finditer(self.sentence_endings, text):
            end_pos = match.end()

            # 检查是否在引号内
            sentence_candidate = text[current_pos:end_pos]

            # 简单的引号平衡检查
            if self._is_sentence_boundary(text, current_pos, end_pos):
                sentences.append(sentence_candidate.strip())
                current_pos = end_pos

                # 跳过标点后的空白字符
                while current_pos < len(text) and text[current_pos].isspace():
                    current_pos += 1

        # 添加剩余文本
        if current_pos < len(text):
            remaining = text[current_pos:].strip()
            if remaining:
                sentences.append(remaining)

        return sentences

    def _is_sentence_boundary(self, text: str, start: int, end: int) -> bool:
        """
        检查是否是真正的句子边界

        Args:
            text: 完整文本
            start: 起始位置
            end: 结束位置

        Returns:
            是否是句子边界
        """
        # 简单的引号配对检查
        segment = text[start:end]

        for open_quote, close_quote in self.quote_pairs:
            open_count = segment.count(open_quote)
            close_count = segment.count(close_quote)

            # 如果引号不配对，可能在引号内，不是句子边界
            if open_count != close_count and open_count > 0:
                return False

        return True

    def _force_split_sentence(self, sentence: str, max_length: int) -> List[str]:
        """
        强制切割超长句子

        Args:
            sentence: 超长句子
            max_length: 最大长度

        Returns:
            切割后的片段列表
        """
        chunks = []
        current_pos = 0

        while current_pos < len(sentence):
            # 尝试在标点符号处切割
            end_pos = min(current_pos + max_length, len(sentence))

            if end_pos == len(sentence):
                # 最后一段
                chunks.append(sentence[current_pos:])
                break

            # 向前查找合适的切割点（标点符号或空格）
            best_cut = end_pos
            for i in range(end_pos - 1, current_pos + max_length // 2, -1):
                if sentence[i] in "，,、；; \t":
                    best_cut = i + 1
                    break

            chunks.append(sentence[current_pos:best_cut])
            current_pos = best_cut

        return chunks

    def remove_extra_whitespace(self, text: str) -> str:
        """
        移除多余的空白字符

        Args:
            text: 输入文本

        Returns:
            清理后的文本
        """
        # 移除行首行尾空白
        text = text.strip()

        # 将多个连续空白字符替换为单个空格
        text = re.sub(r"\s+", " ", text)

        # 移除段落间多余的空行
        text = re.sub(r"\n\s*\n\s*\n", "\n\n", text)

        return text

    def normalize_punctuation(self, text: str) -> str:
        """
        标准化标点符号

        Args:
            text: 输入文本

        Returns:
            标准化后的文本
        """
        # 将英文标点替换为中文标点
        replacements = {
            ",": "，",
            ";": "；",
            ":": "：",
            "!": "！",
            "?": "？",
            "(": "（",
            ")": "）",
        }

        for en_punct, zh_punct in replacements.items():
            text = text.replace(en_punct, zh_punct)

        return text


# 便捷函数
def smart_split_text(
    text: str, max_length: int = 500, min_length: int = 50
) -> List[str]:
    """
    智能切割文本的便捷函数

    Args:
        text: 待切割的文本
        max_length: 最大切割长度
        min_length: 最小切割长度

    Returns:
        切割后的文本片段列表
    """
    processor = TextProcessor()
    return processor.smart_split_text(text, max_length, min_length)


def clean_text(text: str) -> str:
    """
    清理文本的便捷函数

    Args:
        text: 输入文本

    Returns:
        清理后的文本
    """
    processor = TextProcessor()
    text = processor.remove_extra_whitespace(text)
    text = processor.normalize_punctuation(text)
    return text


if __name__ == "__main__":
    # 测试代码已移至 tests/test_text_processor.py
    # 使用 pytest 运行测试：pytest backend/tests/test_text_processor.py
    print("文本处理模块")
    print("请使用 pytest 运行测试：pytest backend/tests/test_text_processor.py")

    # 简单示例
    processor = TextProcessor()
    example_text = "这是一个示例文本。包含多个句子！用于演示文本切割功能？"
    chunks = processor.smart_split_text(example_text, max_length=20)

    print("\n示例切割结果：")
    for i, chunk in enumerate(chunks, 1):
        print(f"{i}. {chunk}")

"""
数据库配置
"""

import os

from ..utils.path_utils import get_resources_root, resolve_resource_path


def get_database_path() -> str:
    """
    获取数据库文件路径

    从环境变量 DATABASE_FILE 读取数据库文件名，默认为 data.db
    数据库文件存储在 resources 目录下

    Returns:
        str: 数据库文件的完整路径
    """
    # 从环境变量获取数据库文件名，默认为 data.db
    db_filename = os.getenv("DATABASE_FILE", "data.db")

    # 使用 path_utils 获取 resources 目录路径
    db_path = resolve_resource_path(db_filename)

    return str(db_path)


def get_voice_meta_db_path(env: str = "production") -> str:
    """
    获取语音元数据数据库路径

    Args:
        env: 环境类型 ("production", "development", "test")

    Returns:
        str: 数据库文件路径
    """
    if env == "production":
        return get_database_path()
    elif env == "development":
        # 开发环境使用带 dev_ 前缀的数据库文件
        dev_db_filename = f"dev_{os.getenv('DATABASE_FILE', 'data.db')}"
        return str(resolve_resource_path(dev_db_filename))
    elif env == "test":
        # 测试环境使用内存数据库
        return ":memory:"
    else:
        raise ValueError(f"不支持的环境类型: {env}")


def get_db_info() -> dict:
    """
    获取数据库配置信息

    Returns:
        dict: 数据库配置信息
    """
    return {
        "production_db": get_database_path(),
        "development_db": get_voice_meta_db_path("development"),
        "test_db": get_voice_meta_db_path("test"),
        "resources_directory": str(get_resources_root()),
        "database_filename": os.getenv("DATABASE_FILE", "data.db"),
    }

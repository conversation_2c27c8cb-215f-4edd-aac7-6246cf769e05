from typing import List

from fastapi import APIRouter, Depends, HTTPException
from src.schemas.tts_schema import (
    TTSEngineInfo,
    TTSSynthesizeRequest,
    TTSSynthesizeResponse,
)

from ..services.tts_service import TTSService
from ..utils.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/tts", tags=["TTS"])

# 使用单例模式，避免重复初始化
_tts_service_instance = None


def get_tts_service() -> TTSService:
    """获取TTS服务实例（单例模式）"""
    global _tts_service_instance
    if _tts_service_instance is None:
        _tts_service_instance = TTSService()
    return _tts_service_instance


@router.get("/engines", response_model=List[TTSEngineInfo])
async def get_engines_info(tts_service: TTSService = Depends(get_tts_service)):
    """获取可用的TTS引擎列表以及他们的参数配置、以及支持的voice列表"""
    try:
        engines = await tts_service.get_engines_info()
        return engines
    except Exception as e:
        logger.error(f"获取TTS引擎列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取引擎列表失败: {str(e)}")


@router.post("/synthesize", response_model=TTSSynthesizeResponse)
async def synthesize_text(
    request: TTSSynthesizeRequest, tts_service: TTSService = Depends(get_tts_service)
):
    """合成语音"""
    try:
        result = await tts_service.synthesize_text(
            engine_type=request.engine_type,
            voice_id_name=request.voice_id_name,
            voice_style_name=request.voice_style_name,
            text=request.text,
            output_path=request.output_path,
            parameters=request.parameters,
        )
        return result
    except ValueError as e:
        logger.error(f"语音合成失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"语音合成失败: {e}")
        raise HTTPException(status_code=500, detail=f"语音合成失败: {str(e)}")

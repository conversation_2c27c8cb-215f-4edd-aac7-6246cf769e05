---
description: FastAPI best practices and patterns for building modern Python web APIs
globs: **/*.py
alwaysApply: false
---

# FastAPI 最佳实践

## 项目结构
- 使用合理的目录结构
- 实施正确的模块组织
- 使用合理的依赖注入
- 按领域组织路由
- 实施正确的中间件
- 使用合理的配置管理

## API 设计
- 使用正确的 HTTP 方法
- 实施正确的状态码
- 使用合理的请求/响应模型
- 实施正确的校验
- 使用合理的错误处理
- 使用 OpenAPI 记录 API

## Model
- 使用 Pydantic Model
- 实施正确的校验
- 使用合理的类型提示
- 保持模型井然有序
- 使用合理的继承
- 实施正确的序列化

## 数据库
- 使用合理的 ORM (SQLAlchemy)
- 实施正确的迁移
- 使用合理的连接池
- 实施正确的事务
- 使用合理的查询优化
- 正确处理数据库错误

## 安全
- 实施正确的 CORS
- 使用合理的速率限制
- 实施正确的输入校验
- 使用合理的安全头部
- 正确处理安全错误
- 实施正确的日志记录

## 性能
- 使用合理的缓存
- 实施正确的异步操作
- 使用合理的后台任务
- 实施正确的连接池
- 使用合理的查询优化
- 监控性能指标

## 测试
- 编写正确的单元测试
- 实施正确的集成测试
- 使用合理的测试装置 (fixtures)
- 实施正确的模拟 (mocking)
- 测试错误场景
- 使用合理的测试覆盖率

## 部署
- 使用合理的环境变量
- 实施正确的日志记录
- 使用合理的监控

## 文档
- 使用合理的文档字符串 (docstrings)
- 实施正确的 API 文档
- 使用合理的类型提示
- 保持文档更新
- 记录错误场景
- 使用合理的版本控制
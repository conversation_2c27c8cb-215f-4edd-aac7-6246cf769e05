#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 VoiceService
"""

from unittest.mock import Mock, patch

import pytest

try:
    from src.db.voice_meta import VoiceMetadata
    from src.schemas.tts_schema import TTSVoice
    from src.services.voice_service import VoiceService
    from src.tts.engine import TTSEngineType
except ImportError:
    from db.voice_meta import VoiceMetadata
    from schemas.tts_schema import TTSVoice
    from services.voice_service import VoiceService


class TestVoiceService:
    """测试 VoiceService 类"""

    def setup_method(self):
        """测试设置"""
        self.voice_service = VoiceService()

    def test_convert_voice_metadata_to_tts_voice(self):
        """测试语音元数据转换"""
        # 创建模拟的 VoiceMetadata
        voice_meta = VoiceMetadata(
            id_name="zh-CN-XiaoxiaoNeural",
            gender="Female",
            display_name="晓晓（女）-中文-内地",
            locale="zh-CN",
            age_group="青年",
            tts_engine="edge-tts",
            description="温柔甜美的女声，适合阅读和对话",
            styles={"通用": "zh-CN-XiaoxiaoNeural-General.wav"},
            exa={},
        )

        # 转换为 TTSVoice
        tts_voice = self.voice_service._convert_voice_metadata_to_tts_voice(voice_meta)

        # 验证转换结果
        assert isinstance(tts_voice, TTSVoice)
        assert tts_voice.id_name == "zh-CN-XiaoxiaoNeural"
        assert tts_voice.gender == "Female"
        assert tts_voice.display_name == "晓晓（女）-中文-内地"
        assert tts_voice.locale == "zh-CN"
        assert tts_voice.age_group == "青年"
        assert tts_voice.description == "温柔甜美的女声，适合阅读和对话"
        assert tts_voice.styles == {"通用": "zh-CN-XiaoxiaoNeural-General.wav"}

    @patch("src.services.voice_service.VoiceMetaDB")
    def test_get_voices_by_engine(self, mock_db_class):
        """测试根据引擎获取语音"""
        # 设置模拟数据库
        mock_db = Mock()
        mock_db_class.return_value = mock_db

        # 模拟数据库返回数据
        mock_voice_meta = VoiceMetadata(
            id_name="zh-CN-XiaoxiaoNeural",
            gender="Female",
            display_name="晓晓（女）-中文-内地",
            locale="zh-CN",
            age_group="青年",
            tts_engine="edge-tts",
            description="温柔甜美的女声",
            styles={"通用": "test.wav"},
            exa={},
        )
        mock_db.get_voices_by_engine.return_value = [mock_voice_meta]

        # 重新创建 VoiceService，这样它会使用模拟的数据库
        voice_service = VoiceService()
        voices = voice_service.get_voices_by_engine(TTSEngineType.EDGE_TTS)

        # 验证结果
        assert len(voices) == 1
        assert isinstance(voices[0], TTSVoice)
        assert voices[0].id_name == "zh-CN-XiaoxiaoNeural"

        # 验证数据库调用
        mock_db.get_voices_by_engine.assert_called_once_with("edge-tts")

    @patch("src.services.voice_service.VoiceMetaDB")
    def test_get_voices_with_filters(self, mock_db_class):
        """测试带过滤条件获取语音"""
        # 设置模拟数据库
        mock_db = Mock()
        mock_db_class.return_value = mock_db

        # 模拟数据库返回数据
        mock_voice_meta = VoiceMetadata(
            id_name="zh-CN-XiaoxiaoNeural",
            gender="Female",
            display_name="晓晓（女）-中文-内地",
            locale="zh-CN",
            age_group="青年",
            tts_engine="edge-tts",
            description="温柔甜美的女声",
            styles={"通用": "test.wav"},
            exa={},
        )
        mock_db.get_voices_by_filters.return_value = [mock_voice_meta]

        # 重新创建 VoiceService
        voice_service = VoiceService()
        voices = voice_service.get_voices(
            engine_type=TTSEngineType.EDGE_TTS, language="zh-CN", gender="Female"
        )

        # 验证结果
        assert len(voices) == 1
        assert isinstance(voices[0], TTSVoice)
        assert voices[0].gender == "Female"

        # 验证数据库调用
        mock_db.get_voices_by_filters.assert_called_once_with(
            tts_engine="edge-tts", gender="Female", age_group=None, locale="zh-CN"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

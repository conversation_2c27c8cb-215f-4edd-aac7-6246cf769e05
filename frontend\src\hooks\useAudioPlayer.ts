import { useState, useRef, useCallback } from 'react';
import { ttsApi } from '../services/api';

interface AudioPlayerState {
  isPlaying: boolean;
  isLoading: boolean;
  currentAudio: string | null;
  error: string | null;
}

export const useAudioPlayer = () => {
  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    isLoading: false,
    currentAudio: null,
    error: null,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 停止当前播放的音频
  const stopCurrentAudio = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }
    setState((prev) => ({
      ...prev,
      isPlaying: false,
      currentAudio: null,
    }));
  }, []);

  // 播放声音文件
  const playVoiceFile = useCallback(
    async (voicePath: string) => {
      try {
        // 如果正在播放同一个文件，则停止播放
        if (state.currentAudio === voicePath && state.isPlaying) {
          stopCurrentAudio();
          return;
        }

        // 停止当前播放的音频
        stopCurrentAudio();

        setState((prev) => ({
          ...prev,
          isLoading: true,
          error: null,
        }));

        // 获取音频文件
        const audioBlob = await ttsApi.getVoiceFile(voicePath);

        if (!audioBlob) {
          throw new Error('无法获取音频文件');
        }

        // 创建音频URL和音频元素
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);

        // 设置音频事件监听器
        audio.onplay = () => {
          setState((prev) => ({
            ...prev,
            isPlaying: true,
            isLoading: false,
            currentAudio: voicePath,
          }));
        };

        audio.onended = () => {
          setState((prev) => ({
            ...prev,
            isPlaying: false,
            currentAudio: null,
          }));
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;
        };

        audio.onerror = () => {
          setState((prev) => ({
            ...prev,
            isPlaying: false,
            isLoading: false,
            currentAudio: null,
            error: '音频播放失败',
          }));
          URL.revokeObjectURL(audioUrl);
          audioRef.current = null;
        };

        audio.onpause = () => {
          setState((prev) => ({
            ...prev,
            isPlaying: false,
          }));
        };

        // 保存音频引用并开始播放
        audioRef.current = audio;
        await audio.play();
      } catch (error) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          isPlaying: false,
          currentAudio: null,
          error: error instanceof Error ? error.message : '播放失败',
        }));
        console.error('播放音频失败:', error);
      }
    },
    [state.currentAudio, state.isPlaying, stopCurrentAudio]
  );

  // 停止播放
  const stopPlaying = useCallback(() => {
    stopCurrentAudio();
  }, [stopCurrentAudio]);

  return {
    ...state,
    playVoiceFile,
    stopPlaying,
    isPlayingFile: (voicePath: string) =>
      state.currentAudio === voicePath && state.isPlaying,
  };
};

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径工具模块
提供统一的路径解析和管理功能
"""

from pathlib import Path
from typing import Union


def get_project_root() -> Path:
    """获取项目根目录路径

    Returns:
        Path: 项目根目录的绝对路径
    """
    # 从当前文件位置（backend/src/utils/path_utils.py）向上查找项目根目录
    # 向上3级：utils -> src -> backend -> project_root
    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent.parent
    return project_root.resolve()


def get_resources_root() -> Path:
    """获取资源目录根路径

    Returns:
        Path: resources目录的绝对路径
    """
    return get_project_root() / "resources"


def resolve_path(path: Union[str, Path], base_dir: str = "project") -> Path:
    """将路径解析为绝对路径

    Args:
        path: 路径字符串或Path对象，可以是相对路径或绝对路径
        base_dir: 基础目录，可选值：
                 - "project": 基于项目根目录（默认）
                 - "resources": 基于resources目录

    Returns:
        Path: 解析后的绝对路径

    Examples:
        >>> resolve_path("data/models")
        PosixPath('/path/to/project/data/models')

        >>> resolve_path("TTS/index-tts", base_dir="resources")
        PosixPath('/path/to/project/resources/TTS/index-tts')
    """
    path_obj = Path(path)

    # 如果已经是绝对路径，直接返回
    if path_obj.is_absolute():
        return path_obj.resolve()

    # 根据base_dir选择基础目录
    if base_dir == "resources":
        base_path = get_resources_root()
    elif base_dir == "project":
        base_path = get_project_root()
    else:
        raise ValueError(
            f"不支持的base_dir: {base_dir}，支持的值: 'project', 'resources'"
        )

    # 将相对路径基于基础目录解析
    resolved_path = base_path / path_obj
    return resolved_path.resolve()


def resolve_resource_path(path: Union[str, Path]) -> Path:
    """将路径解析为基于resources目录的绝对路径（便捷函数）

    Args:
        path: 相对于resources目录的路径

    Returns:
        Path: 解析后的绝对路径

    Examples:
        >>> resolve_resource_path("TTS/index-tts/checkpoints")
        PosixPath('/path/to/project/resources/TTS/index-tts/checkpoints')
    """
    return resolve_path(path, base_dir="resources")


def ensure_dir_exists(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建

    Args:
        path: 目录路径

    Returns:
        Path: 目录的绝对路径
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj.resolve()


def is_relative_to_project(path: Union[str, Path]) -> bool:
    """检查路径是否在项目目录内

    Args:
        path: 要检查的路径

    Returns:
        bool: 如果路径在项目目录内返回True，否则返回False
    """
    try:
        path_obj = Path(path).resolve()
        project_root = get_project_root()
        return project_root in path_obj.parents or path_obj == project_root
    except (OSError, ValueError):
        return False


def get_relative_path(path: Union[str, Path], base_dir: str = "project") -> Path:
    """获取相对于指定基础目录的相对路径

    Args:
        path: 绝对路径或相对路径
        base_dir: 基础目录，可选值："project", "resources"

    Returns:
        Path: 相对路径
    """
    path_obj = Path(path).resolve()

    if base_dir == "resources":
        base_path = get_resources_root()
    elif base_dir == "project":
        base_path = get_project_root()
    else:
        raise ValueError(f"不支持的base_dir: {base_dir}")

    try:
        return path_obj.relative_to(base_path)
    except ValueError:
        # 如果路径不在基础目录下，返回绝对路径
        return path_obj

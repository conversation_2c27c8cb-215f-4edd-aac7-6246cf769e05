"""
测试数据库配置
"""

import os
import sys
from pathlib import Path

from backend.src.tts.engine import TTSEngineType

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from backend.src.db import (  # noqa: E402
    VoiceMetaDB,
    VoiceMetaImporter,
    get_db_info,
    get_voice_meta_db_path,
)


def test_config():
    """测试数据库配置"""
    print("=== 数据库配置测试 ===")

    # 1. 测试配置信息
    print("1. 数据库配置信息:")
    db_info = get_db_info()
    for key, value in db_info.items():
        print(f"   {key}: {value}")

    # 2. 测试不同环境的数据库路径
    print("\n2. 不同环境数据库路径:")
    prod_path = get_voice_meta_db_path("production")
    dev_path = get_voice_meta_db_path("development")
    test_path = get_voice_meta_db_path("test")

    print(f"   生产环境: {prod_path}")
    print(f"   开发环境: {dev_path}")
    print(f"   测试环境: {test_path}")

    # 3. 验证路径是否正确
    print("\n3. 路径验证:")
    expected_filename = os.getenv("DATABASE_FILE", "data.db")
    print(f"   环境变量 DATABASE_FILE: {expected_filename}")
    print(f"   生产环境路径包含文件名: {expected_filename in prod_path}")
    print(f"   生产环境路径包含 resources: {'resources' in prod_path}")

    # 4. 测试数据库实例化
    print("\n4. 测试数据库实例化:")
    try:
        # 使用默认配置（从环境变量）
        db = VoiceMetaDB()
        print(f"   默认数据库路径: {db.db_path}")

        # 使用开发环境配置
        dev_db = VoiceMetaDB(get_voice_meta_db_path("development"))
        print(f"   开发数据库路径: {dev_db.db_path}")

        print("   数据库实例化成功 ✓")
    except Exception as e:
        print(f"   数据库实例化失败: {e}")

    # 5. 测试导入器
    print("\n5. 测试导入器:")
    try:
        importer = VoiceMetaImporter()
        print(f"   导入器数据库路径: {importer.db.db_path}")
        print("   导入器实例化成功 ✓")
    except Exception as e:
        print(f"   导入器实例化失败: {e}")


def test_import_real_data():
    """测试导入真实数据到正式数据库"""
    print("\n=== 导入真实数据测试 ===")

    # 获取 meta.json 文件路径
    meta_json_path = project_root / "resources" / "TTS" / "voices" / "meta.json"

    if not meta_json_path.exists():
        print(f"跳过：meta.json 文件不存在 ({meta_json_path})")
        return

    try:
        # 使用默认配置创建导入器（会使用环境变量配置的数据库）
        importer = VoiceMetaImporter()

        print(f"正在导入到: {importer.db.db_path}")

        # 导入数据
        success = importer.import_from_json_file(str(meta_json_path))

        if success:
            print("✓ 数据导入成功")

            # 显示统计信息
            stats = importer.db.get_engines_stats()
            print("语音统计信息:")
            for engine, count in stats.items():
                print(f"  {engine}: {count} 个语音")

            # 测试查询
            yunxi = importer.db.get_voice_by_id_and_engine(
                "zh-CN-Yunxi", TTSEngineType.INDEX_TTS
            )
            if yunxi:
                print(f"✓ 查询测试成功，找到: {yunxi.display_name}")
            else:
                print("✗ 查询测试失败")
        else:
            print("✗ 数据导入失败")

    except Exception as e:
        print(f"✗ 导入过程出现错误: {e}")
        import traceback

        traceback.print_exc()


def main():
    """主函数"""
    print("鹦鹉小说 - 数据库配置测试")
    print("=" * 50)

    test_config()
    test_import_real_data()

    print("\n测试完成！")


if __name__ == "__main__":
    main()

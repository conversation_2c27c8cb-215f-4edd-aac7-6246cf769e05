# =============================================================================
# ParrotNovels Dev Container - 基础镜像 (简化版)
# =============================================================================

# 使用官方的 Python 开发镜像，包含基本工具
FROM mcr.microsoft.com/devcontainers/python:3.12

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    unzip \
    zip \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 22.x LTS
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# 安装 pnpm
RUN npm install -g pnpm@latest

# 设置工作目录
WORKDIR /workspace

# 暴露端口
EXPOSE 8000 5173 2024

# 默认命令
CMD ["/bin/bash"] 
interface SliderProps {
  label: string;
  value: number;
  min: number;
  max: number;
  step: number;
  unit?: string;
  onChange: (value: number) => void;
}

export default function Slider({
  label,
  value,
  min,
  max,
  step,
  unit = '',
  onChange,
}: SliderProps) {
  const formatValue = (val: number) => {
    if (unit === '%') {
      return `${Math.round(val)}${unit}`;
    }
    if (unit === 'x') {
      return `${val.toFixed(1)}${unit}`;
    }
    return `${val}${unit}`;
  };

  // 计算滑块的百分比位置
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <label className="label mb-0">{label}</label>
        <div className="badge badge-primary text-xs font-mono">
          {formatValue(value)}
        </div>
      </div>

      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="slider w-full"
          style={{
            background: `linear-gradient(to right, rgb(37 99 235) 0%, rgb(37 99 235) ${percentage}%, rgb(229 231 235) ${percentage}%, rgb(229 231 235) 100%)`,
          }}
        />

        {/* 数值标记 */}
        <div className="flex justify-between text-xs text-neutral-400 mt-1">
          <span>{formatValue(min)}</span>
          <span>{formatValue(max)}</span>
        </div>
      </div>
    </div>
  );
}

import json

from fastapi import APIRouter, Depends, HTTPException
from sse_starlette.sse import EventSourceResponse

from ..llm.workflows.novel_processing import NovelProcessingWorkflow
from ..schemas.text_schema import (
    ProcessNovelRequest,
    TextCleanRequest,
    TextCleanResponse,
    TextFormatRequest,
    TextFormatResponse,
)
from ..services.text_service import TextService
from ..utils.logging_config import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/text", tags=["文本处理"])

# 使用单例模式，避免重复初始化
_text_service_instance = None


def get_text_service() -> TextService:
    """获取文本服务实例（单例模式）"""
    global _text_service_instance
    if _text_service_instance is None:
        _text_service_instance = TextService()
    return _text_service_instance


@router.post("/format", response_model=TextFormatResponse)
async def format_novel_content(
    request: TextFormatRequest, text_service: TextService = Depends(get_text_service)
):
    """
    格式化小说内容

    支持的功能：
    - 基本文本清理（移除BOM头、统一换行符、移除多余空格等）
    - 智能章节检测（支持纯数字、中文章节等多种格式）
    - 章节标题格式化（自动识别并美化章节标题）
    - 段落格式化（段落缩进可选）
    - 引号格式化（统一为「」格式）
    """
    try:
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")

        result = text_service.format_novel(
            content=request.content,
            enable_paragraph_indent=request.enable_paragraph_indent,
            indent_spaces=request.indent_spaces,
            enable_chapter_formatting=request.enable_chapter_formatting,
            chapter_pattern=request.chapter_pattern,
            remove_extra_spaces=request.remove_extra_spaces,
            normalize_line_breaks=request.normalize_line_breaks,
            normalize_quotes=request.normalize_quotes,
        )

        return TextFormatResponse(**result)

    except ValueError as e:
        logger.error(f"文本格式化参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"文本格式化失败: {e}")
        raise HTTPException(status_code=500, detail=f"文本格式化失败: {str(e)}")


@router.post("/clean", response_model=TextCleanResponse)
async def clean_text_content(
    request: TextCleanRequest, text_service: TextService = Depends(get_text_service)
):
    """
    清理文本内容

    支持的功能：
    - 移除BOM头
    - 统一换行符
    - 移除多余空白字符
    - 移除多余空行
    """
    try:
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="文本内容不能为空")

        result = text_service.clean_text(
            content=request.content,
            remove_bom=request.remove_bom,
            normalize_line_breaks=request.normalize_line_breaks,
            remove_extra_whitespace=request.remove_extra_whitespace,
            remove_empty_lines=request.remove_empty_lines,
        )

        return TextCleanResponse(**result)

    except ValueError as e:
        logger.error(f"文本清理参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"文本清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"文本清理失败: {str(e)}")


def _get_progress_mapping():
    """获取节点进度映射配置"""
    return {
        "split_chapters": ("分割章节", 5),
        "validate_input": ("验证输入", 5),
        "analyze_characters": ("识别角色", 35),
        "annotate_dialogues": ("标注对话", 25),
        "merge_continuous_segments": ("合并片段", 15),
        "quality_check": ("质量控制", 5),
        "finalize_results": ("完成处理", 10),
    }


def _create_sse_event(event_type: str, data: dict) -> dict:
    """创建SSE事件"""
    return {
        "event": event_type,
        "data": json.dumps(data, ensure_ascii=False),
    }


def _validate_sse_event(event) -> bool:
    """验证SSE事件格式是否正确"""
    if not isinstance(event, dict):
        return False

    # 检查必需的字段
    if "event" not in event or "data" not in event:
        return False

    # 检查是否包含不应该有的字段（如workflow_id）
    allowed_fields = {"event", "data", "id", "retry"}
    for key in event.keys():
        if key not in allowed_fields:
            logger.error(f"SSE事件包含不允许的字段: {key}")
            return False

    return True


def _format_chapter_results(merged_chapters: list, characters: list = None) -> dict:
    """格式化章节结果为用户要求的格式，包含角色列表"""
    formatted_chapters = {}

    for chap in merged_chapters:
        if not isinstance(chap, dict):
            continue

        chapter_index = chap.get("chapter_index", 0)
        chapter_key = f"第{chapter_index}章"
        dialogue_annotations = chap.get("dialogue_annotations", [])

        chapter_dialogues = []

        if isinstance(dialogue_annotations, list):
            for annotation in dialogue_annotations:
                if isinstance(annotation, dict):
                    # 提取需要的四个字段
                    dialogue_item = {
                        "type": annotation.get("type", "narration"),
                        "speaker": annotation.get("speaker", "旁白"),
                        "emotion": annotation.get("emotion", "neutral"),
                        "content": annotation.get("content", ""),
                    }

                    # 确保 speaker 不为空
                    if not dialogue_item["speaker"]:
                        dialogue_item["speaker"] = (
                            "旁白" if dialogue_item["type"] == "narration" else "未知"
                        )

                    chapter_dialogues.append(dialogue_item)

        if chapter_dialogues:
            formatted_chapters[chapter_key] = chapter_dialogues

    # 构建结果，包含章节数据和角色列表
    result = {
        "chapters": formatted_chapters,
        "characters": [],
    }

    # 添加角色列表信息
    if characters and isinstance(characters, list):
        formatted_characters = []
        for char in characters:
            if isinstance(char, dict):
                # 统计该角色在所有章节中的对话数量
                dialogue_count = 0
                for chapter_dialogues in formatted_chapters.values():
                    dialogue_count += len(
                        [
                            d
                            for d in chapter_dialogues
                            if d.get("speaker") == char.get("name")
                        ]
                    )

                formatted_char = {
                    "name": char.get("name", "未知"),
                    "gender": char.get("gender", "未知"),
                    "role_type": char.get("role_type", "配角"),
                    "description": char.get("description", ""),
                    "dialogue_count": dialogue_count,
                }
                formatted_characters.append(formatted_char)

        result["characters"] = formatted_characters
        logger.info(f"格式化了 {len(formatted_characters)} 个角色信息")

    return result


def _get_fallback_result() -> dict:
    """获取备用结果"""
    return {
        "message": "AI处理完成，但未能生成有效的对话标注。请检查输入文本格式。",
        "chapters": {"第0章": []},
        "characters": [],
    }


def _process_stream_events(workflow_instance, initial_state, progress_map):
    """处理流式事件并提取进度信息"""
    final_result = None
    stream_events_worked = False

    try:
        # 尝试使用工作流的 stream 方法，这是最稳定的方式
        for step_result in workflow_instance.stream_run(initial_state):
            stream_events_worked = True

            # 处理每个步骤的结果
            if isinstance(step_result, dict):
                for step_name, step_data in step_result.items():
                    if isinstance(step_data, dict):
                        step_name_clean = step_data.get("step", step_name)

                        # 优先使用工作流的 processing_progress
                        progress = step_data.get("processing_progress")
                        if progress is not None:
                            # 工作流进度是 0-1，转换为 0-100
                            progress_percent = int(progress * 100)
                        else:
                            # 备用方案：使用固定映射
                            progress_percent = progress_map.get(step_name_clean, 0)

                        logger.info(
                            f"步骤: {step_name_clean}, 进度: {progress_percent}%"
                        )

                        # 发送进度更新
                        status_data = {
                            "type": "status",
                            "content": f"步骤: {step_name_clean}...",
                            "progress": progress_percent,
                        }
                        yield _create_sse_event("message", status_data)

                        # 保存步骤数据作为可能的最终结果
                        final_result = step_data

                        # 检查是否为最终结果
                        if (
                            step_name_clean == "finalize_results"
                            or progress_percent >= 100
                        ):
                            stream_events_worked = True

    except Exception as e:
        logger.error(f"流式处理失败: {str(e)}")
        stream_events_worked = False

    # 最后yield结果tuple
    yield (final_result, stream_events_worked)


def _fallback_workflow_execution(workflow_instance, initial_state):
    """备用工作流执行（当流式事件失败时）"""
    logger.warning("流式事件未工作或未获取到结果，尝试直接调用工作流...")

    # 模拟各个步骤的进度更新（备用进度显示）
    steps = [
        ("验证输入", 5),
        ("分割章节", 10),
        ("识别角色", 45),
        ("标注对话", 75),
        ("合并片段", 90),
        ("质量控制", 95),
    ]

    for step_name, progress in steps:
        status_data = {
            "type": "status",
            "content": f"步骤: {step_name}...",
            "progress": progress,
        }
        yield _create_sse_event("message", status_data)

    try:
        # 尝试流式执行获取更精确的进度
        direct_result = None
        for chunk in workflow_instance.stream_run(initial_state):
            # 如果chunk包含进度信息，使用它
            if isinstance(chunk, dict):
                for step_name, step_data in chunk.items():
                    if (
                        isinstance(step_data, dict)
                        and "processing_progress" in step_data
                    ):
                        progress = step_data["processing_progress"]
                        progress_percent = int(progress * 100)

                        status_data = {
                            "type": "status",
                            "content": f"步骤: {step_data.get('step', step_name)}...",
                            "progress": progress_percent,
                        }
                        yield _create_sse_event("message", status_data)

                        # 保存最新的step_data作为结果
                        direct_result = step_data

        # 如果流式执行失败，尝试直接调用
        if not direct_result:
            direct_result = workflow_instance.run(initial_state)

        if direct_result:
            logger.info("通过备用方案获得了结果")
        else:
            direct_result = None

    except Exception as e:
        logger.error(f"备用工作流执行失败: {e}")
        direct_result = None

    # 通过 yield 返回结果而不是 return
    yield direct_result


async def _process_final_result(final_result):
    """处理最终结果并生成响应事件"""
    if not final_result:
        logger.error("工作流已完成，但未捕获到最终结果。")
        error_data = {"message": "处理失败，未生成结果。可能是输入文本格式不符合要求。"}
        yield _create_sse_event("error", error_data)
        return

    logger.info("简化最终结果以便响应。")
    logger.info(
        f"Final result keys: {list(final_result.keys()) if isinstance(final_result, dict) else 'Not a dict'}"
    )

    # 提取章节数据（现在直接使用 annotated_chapters）
    merged_chapters = final_result.get("annotated_chapters", [])
    if not merged_chapters:
        logger.warning("annotated_chapters 为空，尝试其他字段...")
        merged_chapters = final_result.get("chapters", [])

    # 提取角色数据
    characters = final_result.get("characters", [])
    logger.info(f"从工作流结果中提取到 {len(characters)} 个角色")

    # 格式化结果（包含角色信息）
    simplified_result = _format_chapter_results(merged_chapters, characters)

    # 如果仍然没有数据，提供基本的反馈
    if not simplified_result or not simplified_result.get("chapters"):
        logger.warning("没有找到有效的章节数据，提供基本反馈")
        simplified_result = _get_fallback_result()

    # 发送完成状态
    status_data = {"type": "status", "content": "处理完成", "progress": 100}
    yield _create_sse_event("message", status_data)

    # 发送最终结果
    final_data = {
        "type": "result",
        "content": simplified_result,
        "progress": 100,
    }
    yield _create_sse_event("final", final_data)
    logger.info("最终结果已发送至客户端。")


@router.post("/process-novel", summary="AI识别与分割(流式)")
async def process_novel_stream(
    request: ProcessNovelRequest,
):
    """
    接收小说文本，通过LangGraph工作流处理，并使用SSE流式返回进度和结果。

    - **content**: 要处理的小说全文。
    - **title**: 小说标题 (可选)。
    """
    logger.info(f"接收到小说处理请求，文本长度: {len(request.content)}")

    async def event_generator():
        progress_map = _get_progress_mapping()

        try:
            # 发送初始状态
            initial_data = {
                "type": "status",
                "content": "正在初始化处理流程...",
                "progress": 0,
            }
            yield _create_sse_event("message", initial_data)
            logger.info("启动小说处理流...")

            # 创建工作流实例并获取正确的初始状态
            workflow_instance = NovelProcessingWorkflow(use_checkpointer=False)
            initial_state = workflow_instance.get_initial_state(
                novel_content=request.content, novel_title=request.title or "未命名小说"
            )

            # 尝试使用流式事件处理
            final_result = None
            stream_events_worked = False

            for event_result in _process_stream_events(
                workflow_instance, initial_state, progress_map
            ):
                if isinstance(event_result, tuple):
                    final_result, stream_events_worked = event_result
                    break
                else:
                    # 验证并yield事件
                    if _validate_sse_event(event_result):
                        yield event_result
                    else:
                        logger.warning(
                            f"收到无效的SSE事件格式: {type(event_result)}, {event_result}"
                        )
                        continue

            # 如果流式事件失败，使用备用方案
            if not stream_events_worked or final_result is None:
                for event in _fallback_workflow_execution(
                    workflow_instance, initial_state
                ):
                    # 如果是SSE事件，验证并yield
                    if _validate_sse_event(event):
                        yield event
                    else:
                        # 否则当作最终结果处理
                        logger.info(f"备用工作流返回了最终结果: {type(event)}")
                        final_result = event

            # 处理并发送最终结果
            async for event in _process_final_result(final_result):
                # 验证并yield事件
                if _validate_sse_event(event):
                    yield event
                else:
                    logger.warning(
                        f"最终结果处理返回了无效的SSE事件格式: {type(event)}, {event}"
                    )
                    continue

        except Exception as e:
            logger.exception("小说处理流中发生错误。")
            error_data = {"message": f"处理过程中发生错误: {str(e)}"}
            yield _create_sse_event("error", error_data)

    return EventSourceResponse(event_generator())

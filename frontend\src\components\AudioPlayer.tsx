import { useTTSStore } from '../store/ttsStore';
import { useEffect, useRef, useState } from 'react';
import { Play, Pause, SkipBack, SkipForward } from 'lucide-react';

export default function AudioPlayer() {
  const { audioPlayer, updateAudioPlayer, currentAudioUrl, novel } =
    useTTSStore();
  const audioRef = useRef<HTMLAudioElement>(null);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const volumeControlRef = useRef<HTMLDivElement>(null);

  // 监听store中音频URL变化
  useEffect(() => {
    if (currentAudioUrl && audioRef.current) {
      // 如果URL是相对路径，添加baseURL
      const baseUrl =
        import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const fullUrl = currentAudioUrl.startsWith('http')
        ? currentAudioUrl
        : `${baseUrl}${currentAudioUrl}`;

      console.log('设置音频URL:', fullUrl);
      audioRef.current.src = fullUrl;
      audioRef.current.load();
    }
  }, [currentAudioUrl]);

  // 监听音频播放状态变化
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => {
      updateAudioPlayer({ currentTime: Math.floor(audio.currentTime) });
    };

    const handleDurationChange = () => {
      updateAudioPlayer({ duration: Math.floor(audio.duration) });
    };

    const handleEnded = () => {
      updateAudioPlayer({ isPlaying: false, currentTime: 0 });
    };

    const handleCanPlay = () => {
      // 音频加载完成，可以播放
      console.log('音频加载完成');
    };

    const handleLoadError = (e: any) => {
      console.error('音频加载失败:', e);
      console.error('音频URL:', audio.src);
    };

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('durationchange', handleDurationChange);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleLoadError);

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('durationchange', handleDurationChange);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleLoadError);
    };
  }, [updateAudioPlayer]);

  // 同步播放状态
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (audioPlayer.isPlaying) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [audioPlayer.isPlaying]);

  // 同步播放速度
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.playbackRate = audioPlayer.playbackSpeed;
    }
  }, [audioPlayer.playbackSpeed]);

  // 同步音量
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = audioPlayer.volume;
    }
  }, [audioPlayer.volume]);

  // 点击外部关闭音量滑块
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        volumeControlRef.current &&
        !volumeControlRef.current.contains(event.target as Node)
      ) {
        setShowVolumeSlider(false);
      }
    };

    if (showVolumeSlider) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showVolumeSlider]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const togglePlay = () => {
    if (!currentAudioUrl) {
      alert('请先生成语音');
      return;
    }
    updateAudioPlayer({ isPlaying: !audioPlayer.isPlaying });
  };

  const handleSpeedChange = (speed: number) => {
    updateAudioPlayer({ playbackSpeed: speed });
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = (parseFloat(e.target.value) / 100) * audioPlayer.duration;
    updateAudioPlayer({ currentTime: newTime });

    // 同步到实际音频播放位置
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
  };

  const handleDownload = async () => {
    if (!currentAudioUrl) return;

    setIsDownloading(true);
    try {
      // 构建完整的音频URL
      const fullUrl = currentAudioUrl.startsWith('http')
        ? currentAudioUrl
        : `http://localhost:8000${currentAudioUrl}`;

      // 使用 fetch 获取音频文件
      const response = await fetch(fullUrl);
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }

      // 获取音频文件的 blob
      const blob = await response.blob();

      // 生成有意义的文件名
      const timestamp = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
        .replace(/[\/\s:]/g, '-');

      const novelTitle = novel.title || 'TTS音频';
      const filename = `${novelTitle}_${timestamp}.wav`;

      // 创建下载链接
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理临时URL
      URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('下载失败:', error);
      alert(`下载失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value) / 100;
    updateAudioPlayer({ volume: newVolume });
  };

  const toggleMute = () => {
    if (audioPlayer.volume > 0) {
      updateAudioPlayer({ volume: 0 });
    } else {
      updateAudioPlayer({ volume: 1.0 });
    }
  };

  const getVolumeIcon = () => {
    if (audioPlayer.volume === 0) return '🔇';
    if (audioPlayer.volume < 0.5) return '🔉';
    return '🔊';
  };

  const progressPercentage =
    audioPlayer.duration > 0
      ? (audioPlayer.currentTime / audioPlayer.duration) * 100
      : 0;

  return (
    <div className="card card-hover animate-in">
      {/* 隐藏的音频元素 */}
      <audio ref={audioRef} preload="auto" />

      <div className="panel-header py-2 pl-[8px] pr-[8px] pt-[4px] pb-[4px]">
        <h2 className="panel-title text-sm">🎧 语音预览</h2>
        {currentAudioUrl && (
          <span className="text-xs text-green-600">✓ 音频已加载</span>
        )}
      </div>

      <div className="panel-body py-1">
        {/* 进度条 */}
        <div className="mb-2">
          <div className="flex justify-between text-xs text-neutral-500 mb-1">
            <span>{formatTime(audioPlayer.currentTime)}</span>
            <span>{formatTime(audioPlayer.duration)}</span>
          </div>

          <input
            type="range"
            min="0"
            max="100"
            value={progressPercentage}
            onChange={handleProgressChange}
            className="range range-primary range-xs w-full"
          />
        </div>

        {/* 播放速度、播放控制和音量控制 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs text-neutral-500">播放速度:</span>
            <div className="flex items-center gap-1">
              <button
                onClick={() => handleSpeedChange(0.75)}
                className={`btn btn-xs ${audioPlayer.playbackSpeed === 0.75 ? 'btn-primary' : 'btn-ghost'}`}
              >
                0.75x
              </button>
              <button
                onClick={() => handleSpeedChange(1.0)}
                className={`btn btn-xs ${audioPlayer.playbackSpeed === 1.0 ? 'btn-primary' : 'btn-ghost'}`}
              >
                1.0x
              </button>
              <button
                onClick={() => handleSpeedChange(1.25)}
                className={`btn btn-xs ${audioPlayer.playbackSpeed === 1.25 ? 'btn-primary' : 'btn-ghost'}`}
              >
                1.25x
              </button>
              <button
                onClick={() => handleSpeedChange(1.5)}
                className={`btn btn-xs ${audioPlayer.playbackSpeed === 1.5 ? 'btn-primary' : 'btn-ghost'}`}
              >
                1.5x
              </button>
              <button
                onClick={() => handleSpeedChange(2.0)}
                className={`btn btn-xs ${audioPlayer.playbackSpeed === 2.0 ? 'btn-primary' : 'btn-ghost'}`}
              >
                2.0x
              </button>
            </div>
          </div>

          {/* 播放控制按钮 - 移到中间位置 */}
          <div className="flex items-center gap-1">
            <button className="btn btn-ghost btn-xs">
              <SkipBack className="w-4 h-4" />
            </button>
            <button onClick={togglePlay} className="btn btn-primary btn-sm">
              {audioPlayer.isPlaying ? (
                <Pause className="w-4 h-4" />
              ) : (
                <Play className="w-4 h-4" />
              )}
            </button>
            <button className="btn btn-ghost btn-xs">
              <SkipForward className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center gap-2">
            {/* 音量控制 */}
            <div className="relative" ref={volumeControlRef}>
              <button
                onClick={() => setShowVolumeSlider(!showVolumeSlider)}
                onDoubleClick={toggleMute}
                className="btn btn-ghost btn-xs p-[4px] rounded-[7px]"
                title={`音量: ${Math.round(audioPlayer.volume * 100)}% (双击静音)`}
              >
                {getVolumeIcon()}
              </button>

              {/* 音量滑块 */}
              {showVolumeSlider && (
                <div className="absolute bottom-full mb-4 left-1/2 transform -translate-x-1/2 bg-white shadow-lg rounded-lg px-4 py-3 pb-5 z-10 border">
                  <div className="flex flex-col items-center w-6">
                    <span className="text-xs text-gray-600 whitespace-nowrap mb-6">
                      {Math.round(audioPlayer.volume * 100)}%
                    </span>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={audioPlayer.volume * 100}
                      onChange={handleVolumeChange}
                      className="range range-primary range-xs w-15 rotate-[-90deg]"
                      style={{ transformOrigin: 'center' }}
                    />
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={handleDownload}
              disabled={!currentAudioUrl || isDownloading}
              className="btn btn-ghost btn-xs p-[4px]"
              title={isDownloading ? '下载中...' : '下载音频文件'}
            >
              {isDownloading ? (
                <span className="loading loading-spinner loading-xs"></span>
              ) : (
                '📥'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

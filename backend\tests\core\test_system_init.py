#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统初始化模块测试
"""

import os
import sys
from pathlib import Path

import pytest

# 添加src到路径，以便导入模块
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.system_init import (
    SystemInitializer,
    get_system_initializer,
    initialize_system,
    is_system_initialized,
)


class TestSystemInitializer:
    """系统初始化器测试类"""

    def test_system_initializer_creation(self):
        """测试系统初始化器创建"""
        initializer = SystemInitializer()
        assert initializer is not None
        assert not initializer.is_initialized()
        assert initializer.logger is None
        assert initializer.tts_manager is None

    def test_get_system_initializer_singleton(self):
        """测试获取系统初始化器单例"""
        initializer1 = get_system_initializer()
        initializer2 = get_system_initializer()

        # 应该返回同一个实例
        assert initializer1 is initializer2

    def test_environment_loading(self):
        """测试环境变量加载"""
        initializer = SystemInitializer()

        # 执行环境变量加载
        initializer._load_environment()

        # 检查默认环境变量是否设置
        assert os.getenv("TTS_ENABLED_ENGINES") is not None
        assert os.getenv("LOG_LEVEL") is not None
        assert os.getenv("LOG_FILE") is not None

    def test_set_default_env_vars(self):
        """测试设置默认环境变量"""
        initializer = SystemInitializer()

        # 清除可能存在的环境变量
        test_var = "TTS_ENABLED_ENGINES"
        original_value = os.getenv(test_var)
        if test_var in os.environ:
            del os.environ[test_var]

        # 设置默认值
        initializer._set_default_env_vars()

        # 检查是否设置了默认值
        assert os.getenv(test_var) == "edge,index,minimax"

        # 恢复原始值
        if original_value is not None:
            os.environ[test_var] = original_value

    def test_basic_logging_setup(self):
        """测试基本日志配置"""
        initializer = SystemInitializer()

        # 设置测试环境变量
        os.environ["LOG_LEVEL"] = "DEBUG"
        os.environ["LOG_FILE"] = "logs/test.log"

        # 执行日志配置
        initializer._setup_basic_logging()

        # 检查日志器是否创建
        assert initializer.logger is not None

        # 清理
        del os.environ["LOG_LEVEL"]
        del os.environ["LOG_FILE"]

    def test_system_initialization(self):
        """测试完整的系统初始化"""
        # 注意：这个测试可能会失败，因为需要真实的TTS引擎
        # 在CI/CD环境中可能需要跳过

        if "CI" in os.environ or "GITHUB_ACTIONS" in os.environ:
            pytest.skip("跳过CI环境中的完整初始化测试")

        success = initialize_system()

        # 在测试环境中，可能某些引擎无法初始化，但系统应该能启动
        # 这里主要测试没有致命错误
        assert isinstance(success, bool)

    def test_is_system_initialized(self):
        """测试系统初始化状态检查"""
        # 初始状态应该是未初始化
        # 注意：由于单例模式，可能已经被其他测试初始化了
        status = is_system_initialized()
        assert isinstance(status, bool)

    def test_initialization_with_invalid_config(self):
        """测试无效配置的初始化"""
        initializer = SystemInitializer()

        # 设置无效的日志级别
        os.environ["LOG_LEVEL"] = "INVALID_LEVEL"

        # 应该仍然能够初始化（使用默认值）
        initializer._load_environment()
        initializer._setup_logging()

        assert initializer.logger is not None

        # 清理
        del os.environ["LOG_LEVEL"]


class TestSystemInitializationIntegration:
    """系统初始化集成测试"""

    def test_environment_and_logging_integration(self):
        """测试环境变量和日志的集成"""
        initializer = SystemInitializer()

        # 执行环境加载和日志配置
        initializer._load_environment()
        initializer._setup_logging()

        # 验证组件正常工作
        assert initializer.logger is not None

        # 测试日志记录
        initializer.logger.info("测试日志消息")

        # 应该不抛出异常


if __name__ == "__main__":
    pytest.main([__file__])

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行小说处理测试的便捷脚本
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "backend" / "src"))


def main():
    """主函数"""
    print("=" * 60)
    print("小说处理工作流测试")
    print("=" * 60)

    try:
        # 导入测试函数
        from test_novel_processing import (
            test_chapter_splitting,
            test_novel_processing_with_file,
        )

        print("1. 开始章节分割功能测试...")
        print("-" * 40)
        test_chapter_splitting()

        print("\n2. 开始完整小说处理测试...")
        print("-" * 40)
        test_novel_processing_with_file()

        print("\n" + "=" * 60)
        print("🎉 所有测试成功完成!")
        print("=" * 60)

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

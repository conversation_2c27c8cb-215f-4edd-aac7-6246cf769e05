"""重构后的小说处理工作流

主要负责工作流编排和状态管理，具体功能由各个模块处理
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from .chapter_processors import ChapterProcessor
from .character_analyzer import CharacterAnalyzer
from .dialogue_processor import DialogueProcessor
from .post_processor import PostProcessor
from .quality_controller import QualityController
from .result_finalizer import ResultFinalizer

try:
    from langgraph.graph import END, StateGraph
except ImportError:
    StateGraph = None
    END = None

from ...utils.logging_config import get_logger
from .base import BaseWorkflow, BaseWorkflowState

logger = get_logger(__name__)


class NovelProcessingState(BaseWorkflowState):
    """小说处理工作流状态"""

    # 输入数据
    novel_content: str
    novel_title: Optional[str]

    # 章节分割结果
    chapter_contents: List[str]  # 章节原始内容列表

    # 处理结果
    characters: List[Dict[str, Any]]
    original_characters: Optional[List[Dict[str, Any]]]  # 保存原始角色用于质量对比
    chapters: List[Dict[str, Any]]
    annotated_chapters: List[Dict[str, Any]]
    merged_chapters: List[Dict[str, Any]]
    overall_analysis: Optional[Dict[str, Any]]

    # 质量控制
    quality_scores: Dict[str, float]
    retry_count: int

    # 进度跟踪
    current_chapter: int
    total_chapters: int
    processing_progress: float


class NovelProcessingWorkflow(BaseWorkflow):
    """小说处理工作流 - 重构版本"""

    def __init__(self, llm_client=None, use_checkpointer: bool = True):
        # 初始化各个功能模块（在调用super().__init__()之前）
        self.chapter_processor = ChapterProcessor(self)
        self.character_analyzer = CharacterAnalyzer(self)
        self.dialogue_processor = DialogueProcessor(self)
        self.post_processor = PostProcessor(self)
        self.quality_controller = QualityController(self)
        self.result_finalizer = ResultFinalizer(self)

        super().__init__(llm_client=llm_client, use_checkpointer=use_checkpointer)

    def _setup_workflow(self):
        """设置工作流图"""
        if not StateGraph:
            return

        # 创建状态图
        workflow = StateGraph(NovelProcessingState)

        # 添加节点 - 使用各模块的方法
        workflow.add_node("split_chapters", self.chapter_processor.split_chapters)
        workflow.add_node("validate_input", self.chapter_processor.validate_input)
        workflow.add_node(
            "analyze_characters", self.character_analyzer.analyze_characters
        )
        workflow.add_node(
            "annotate_dialogues", self.dialogue_processor.annotate_dialogues
        )
        workflow.add_node(
            "merge_continuous_segments",
            self.dialogue_processor.merge_continuous_segments,
        )
        workflow.add_node("post_process", self.post_processor.post_process)
        workflow.add_node("quality_check", self.quality_controller.quality_check)
        workflow.add_node("retry_processing", self.quality_controller.retry_processing)
        workflow.add_node("finalize_results", self.result_finalizer.finalize_results)

        # 设置入口点
        workflow.set_entry_point("split_chapters")

        # 添加边
        workflow.add_edge("split_chapters", "validate_input")
        workflow.add_edge("validate_input", "analyze_characters")
        workflow.add_edge("analyze_characters", "annotate_dialogues")
        workflow.add_edge("annotate_dialogues", "merge_continuous_segments")
        workflow.add_edge("merge_continuous_segments", "post_process")
        workflow.add_edge("post_process", "quality_check")

        # 条件边：质量检查后的分支
        workflow.add_conditional_edges(
            "quality_check",
            self.quality_controller.decide_after_quality_check,
            {
                "retry": "retry_processing",
                "finalize": "finalize_results",
                "failed": END,
            },
        )

        workflow.add_edge("retry_processing", "annotate_dialogues")
        workflow.add_edge("finalize_results", END)

        # 编译图
        self.graph = workflow.compile(checkpointer=self.checkpointer)

    def visualize_ascii(self) -> str:
        """生成 ASCII 流程图"""
        if not self.graph:
            return "流程图未初始化"

        return self.graph.get_graph().draw_ascii()

    @staticmethod
    def extract_title_from_filename(filename_or_path: str) -> str:
        """
        从文件名或文件路径中提取小说标题

        Args:
            filename_or_path: 文件名或文件路径

        Returns:
            提取的小说标题
        """
        # 获取文件名（去掉路径）
        filename = Path(filename_or_path).name

        # 去掉文件扩展名
        title = Path(filename).stem

        # 清理标题：去掉常见的无关词汇
        # 移除版本号、编号等
        import re

        title = re.sub(
            r"[_\-\s]*(?:v\d+|版本\d+|第?\d+版|完整版|精校版|全本|txt|TXT).*$",
            "",
            title,
        )
        title = re.sub(r"^[\d\-_\s]+", "", title)  # 移除开头的数字、下划线、横线
        title = title.strip("_-. ")  # 移除首尾的特殊字符

        # 如果清理后为空，则使用原文件名（不含扩展名）
        if not title:
            title = Path(filename).stem

        return title or "未命名小说"

    def get_initial_state(
        self,
        novel_content: str,
        novel_title: Optional[str] = None,
        source_file: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        获取初始状态

        Args:
            novel_content: 小说内容
            novel_title: 小说标题（可选）
            source_file: 源文件路径（可选，用于自动提取标题）
            **kwargs: 其他元数据
        """
        # 自动提取标题：优先使用提供的标题，其次从文件路径提取，最后使用默认值
        if not novel_title and source_file:
            novel_title = self.extract_title_from_filename(source_file)
        elif not novel_title:
            novel_title = "未命名小说"

        return {
            "workflow_id": self.create_workflow_id(),
            "step": "split_chapters",
            "status": "running",
            "error": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "metadata": {**kwargs, "source_file": source_file},
            # 小说数据
            "novel_content": novel_content,
            "novel_title": novel_title,
            # 章节分割结果
            "chapter_contents": [],
            # 初始化结果
            "characters": [],
            "original_characters": None,  # 将在后处理时填充
            "chapters": [],
            "annotated_chapters": [],
            "merged_chapters": [],
            "overall_analysis": None,
            # 质量控制
            "quality_scores": {},
            "retry_count": 0,
            # 进度跟踪
            "current_chapter": 0,
            "total_chapters": 0,
            "processing_progress": 0.0,
        }


# For LangGraph Studio visualization
# Create an instance of the workflow to get the graph object.
# The graph is compiled in the constructor of the BaseWorkflow.
# We pass `use_checkpointer=False` because LangGraph Studio/API handles persistence.
try:
    workflow_instance = NovelProcessingWorkflow(use_checkpointer=False)
    app = workflow_instance.graph
except Exception as e:
    logger.error(f"Failed to create workflow instance for visualization: {e}")
    app = None

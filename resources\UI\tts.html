<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音转换 - Parrots TTS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333333;
            overflow: hidden;
            height: 100vh;
        }

        /* 桌面应用标题栏 */
        .title-bar {
            background: #e1e1e1;
            height: 32px;
            display: flex;
            align-items: center;
            padding: 0 12px;
            border-bottom: 1px solid #d1d1d1;
            -webkit-app-region: drag;
        }

        .title-bar .app-title {
            font-size: 13px;
            color: #333333;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        /* 主容器 */
        .app-container {
            height: calc(100vh - 32px);
            display: grid;
            grid-template-columns: 260px 1fr 280px;
            background: #ffffff;
        }

        /* 侧边栏通用样式 */
        .sidebar {
            background: #f8f8f8;
            border-right: 1px solid #e1e1e1;
            padding: 12px;
            overflow-y: auto;
        }

        .sidebar:last-child {
            border-right: none;
            border-left: 1px solid #e1e1e1;
        }

        /* 面板 */
        .panel {
            background: #ffffff;
            border: 1px solid #e1e1e1;
            border-radius: 6px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .panel-header {
            background: #f1f3f4;
            padding: 8px 12px;
            font-size: 13px;
            font-weight: 600;
            border-bottom: 1px solid #e1e1e1;
            color: #333333;
        }

        .panel-content {
            padding: 12px;
        }

        /* 表单控件 */
        .form-group {
            margin-bottom: 12px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            font-size: 12px;
            color: #555555;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            background: #ffffff;
            border: 1px solid #d1d1d1;
            border-radius: 4px;
            color: #333333;
            font-size: 13px;
            outline: none;
            height: 36px;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
        }

        /* 滑块 */
        .slider-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .slider {
            flex: 1;
            height: 20px;
            background: #e1e1e1;
            outline: none;
            border-radius: 10px;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #0066cc;
            cursor: pointer;
        }

        .slider-value {
            font-size: 12px;
            color: #666666;
            min-width: 40px;
            text-align: right;
            font-weight: 500;
        }

        /* 按钮 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            font-weight: 500;
            outline: none;
            transition: all 0.2s;
            height: 36px;
            box-sizing: border-box;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: #0066cc;
            color: white;
        }

        .btn-primary:hover {
            background: #0052a3;
        }

        .btn-secondary {
            background: #f1f3f4;
            color: #333333;
            border: 1px solid #d1d1d1;
        }

        .btn-secondary:hover {
            background: #e8eaed;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 11px;
        }

        .btn-icon {
            width: 24px;
            height: 24px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f1f3f4;
            border: 1px solid #d1d1d1;
            color: #333333;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-icon:hover {
            background: #e8eaed;
        }

        /* 主内容区 */
        .main-content {
            padding: 12px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow-y: auto;
            background: #ffffff;
        }

        .content-header {
            display: flex;
            gap: 12px;
            align-items: end;
        }

        .novel-meta {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 200px;
            gap: 12px;
        }

        .content-actions {
            display: flex;
            gap: 8px;
        }

        /* 文本区域 */
        .novel-textarea {
            width: 100%;
            height: 480px;
            padding: 12px;
            background: #ffffff;
            border: 1px solid #d1d1d1;
            border-radius: 6px;
            color: #333333;
            font-size: 14px;
            font-family: 'Consolas', 'Monaco', monospace;
            line-height: 1.6;
            resize: none;
            outline: none;
        }

        .novel-textarea:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
        }

        /* 播放器 */
        .audio-player {
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 6px;
            padding: 12px;
        }

        .player-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: #f1f3f4;
            color: #333333;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            border: 1px solid #d1d1d1;
        }

        .control-btn:hover {
            background: #e8eaed;
        }

        .control-btn.play-pause {
            background: #0066cc;
            color: white;
            border-color: #0066cc;
        }

        .control-btn.play-pause:hover {
            background: #0052a3;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e1e1e1;
            border-radius: 2px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: #0066cc;
            transition: width 0.3s;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #666666;
            margin-bottom: 8px;
        }

        .player-info {
            font-size: 12px;
            color: #333333;
        }

        /* 角色配置 */
        .character-item {
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 8px;
        }

        .character-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .character-name {
            font-size: 12px;
            font-weight: 600;
            color: #333333;
        }

        .character-percentage {
            font-size: 11px;
            color: #666666;
        }

        .voice-select-group {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .voice-select-group select {
            flex: 1;
            font-size: 11px;
            padding: 4px 6px;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <!-- 桌面应用标题栏 -->
    <div class="title-bar">
        <div class="app-title">
            <span>🦜</span>
            <span>Parrots TTS</span>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 左侧TTS设置 -->
        <div class="sidebar">
            <div class="panel">
                <div class="panel-header">🎛️ TTS设置</div>
                <div class="panel-content">
                    <div class="form-group">
                        <label class="form-label">TTS引擎</label>
                        <select class="form-control">
                            <option>Edge-TTS</option>
                            <option>Index-TTS</option>
                            <option>Fish-TTS</option>
                            <option>Mimimax-2hd</option>
                            <option>Mimimax-2turbo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">语速</label>
                        <div class="slider-container">
                            <input type="range" class="slider" min="0.5" max="2" value="1" step="0.1">
                            <span class="slider-value">1.0x</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">音调</label>
                        <div class="slider-container">
                            <input type="range" class="slider" min="0.5" max="2" value="1" step="0.1">
                            <span class="slider-value">1.0x</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">音量</label>
                        <div class="slider-container">
                            <input type="range" class="slider" min="0" max="100" value="80">
                            <span class="slider-value">80%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间主内容区 -->
        <div class="main-content">
            <!-- 头部控件 -->
            <div class="content-header">
                <div class="novel-meta">
                    <div class="form-group">
                        <label class="form-label">小说名称</label>
                        <input type="text" class="form-control" placeholder="请输入小说名称" value="夏日邂逅">
                    </div>
                    <div class="form-group">
                        <label class="form-label">小说分类</label>
                        <select class="form-control">
                            <option>都市言情</option>
                            <option>玄幻修仙</option>
                            <option>科幻未来</option>
                            <option>历史穿越</option>
                            <option>悬疑推理</option>
                            <option>武侠江湖</option>
                        </select>
                    </div>
                </div>
                <div class="content-actions">
                    <button class="btn btn-secondary">🔍 AI识别角色</button>
                    <button class="btn btn-primary">🎵 开始转换</button>
                </div>
            </div>

            <!-- 小说内容 -->
            <div class="form-group">
                <label class="form-label">小说内容</label>
                <textarea class="novel-textarea" placeholder="请粘贴或输入小说内容...">第一章 初遇

夏日的午后，阳光透过梧桐叶片洒在石板路上，形成斑驳的光影。林小雨匆匆走过这条熟悉的小巷，手中紧握着刚刚收到的录取通知书。

"真的吗？我还以为你不会来了呢。"林小雨看着眼前的男子，眼中闪烁着惊喜的光芒。

"抱歉让你久等了，路上有点堵车。"陈浩然温和地说道，伸手帮她整理了一下被风吹乱的头发。

就在这时，李阿姨从旁边走过，看到两人亲密的样子，笑着说道："小雨啊，这个小伙子看起来不错呢。"

林小雨脸颊微红，低声说道："李阿姨，您别乱说..."

陈浩然也有些不好意思，但还是礼貌地向李阿姨点头致意。

夕阳西下，两人并肩走在回家的路上，空气中弥漫着淡淡的花香，一切都显得那么美好...</textarea>
            </div>

            <!-- 语音预览播放器 -->
            <div class="panel">
                <div class="panel-header">🎧 语音预览</div>
                <div class="panel-content">
                    <div class="audio-player">
                        <div class="player-controls">
                            <button class="control-btn">⏮️</button>
                            <button class="control-btn play-pause">▶️</button>
                            <button class="control-btn">⏭️</button>
                        </div>
                        <div class="time-display">
                            <span>02:15</span>
                            <span>08:42</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 25%"></div>
                        </div>
                        <div class="player-info">
                            <div style="margin-bottom: 4px;">当前: 林小雨 (晓萱)</div>
                            <div style="font-size: 11px; color: #666666;">"真的吗？我还以为你不会来了呢。"</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧角色声音配置 -->
        <div class="sidebar">
            <div class="panel">
                <div class="panel-header">🎭 角色声音配置</div>
                <div class="panel-content">
                    <div class="form-group">
                        <label class="form-label">分配模式</label>
                        <select class="form-control">
                            <option value="manual">手动选择声音</option>
                            <option value="auto">AI自动分配</option>
                        </select>
                    </div>

                    <!-- 角色列表 -->
                    <div class="character-item">
                        <div class="character-header">
                            <span class="character-name">📖 旁白</span>
                            <span class="character-percentage">45%</span>
                        </div>
                        <div class="voice-select-group">
                            <select class="form-control voice-select">
                                <option>云希 (女声) - 温和知性</option>
                                <option>云扬 (男声) - 沉稳磁性</option>
                                <option>晓萱 (女声) - 清甜活泼</option>
                            </select>
                            <button class="btn-icon" title="试听">▶️</button>
                        </div>
                    </div>

                    <div class="character-item">
                        <div class="character-header">
                            <span class="character-name">👩 林小雨</span>
                            <span class="character-percentage">28%</span>
                        </div>
                        <div class="voice-select-group">
                            <select class="form-control voice-select">
                                <option>晓萱 (女声) - 清甜活泼</option>
                                <option>小云 (女声) - 温柔甜美</option>
                                <option>小薇 (女声) - 青春洋溢</option>
                            </select>
                            <button class="btn-icon" title="试听">▶️</button>
                        </div>
                    </div>

                    <div class="character-item">
                        <div class="character-header">
                            <span class="character-name">👨 陈浩然</span>
                            <span class="character-percentage">22%</span>
                        </div>
                        <div class="voice-select-group">
                            <select class="form-control voice-select">
                                <option>云健 (男声) - 阳光帅气</option>
                                <option>晓辰 (男声) - 温和儒雅</option>
                                <option>云峰 (男声) - 成熟稳重</option>
                            </select>
                            <button class="btn-icon" title="试听">▶️</button>
                        </div>
                    </div>

                    <div class="character-item">
                        <div class="character-header">
                            <span class="character-name">👩‍🦳 李阿姨</span>
                            <span class="character-percentage">5%</span>
                        </div>
                        <div class="voice-select-group">
                            <select class="form-control voice-select">
                                <option>晓颖 (女声) - 成熟稳重</option>
                                <option>云美 (女声) - 温和慈祥</option>
                                <option>小丽 (女声) - 亲切和蔼</option>
                            </select>
                            <button class="btn-icon" title="试听">▶️</button>
                        </div>
                    </div>

                    <div style="display: flex; gap: 8px; margin-top: 12px;">
                        <button class="btn btn-secondary btn-small">🎲 重新分配</button>
                        <button class="btn btn-secondary btn-small">💾 保存配置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 
"""
API Schemas 模块

提供Pydantic模型定义
"""

from .text_schema import (
    TextCleanRequest,
    TextCleanResponse,
    TextFormatRequest,
    TextFormatResponse,
)
from .tts_schema import (
    TTSEngine,
    TTSEngineInfo,
    TTSParameter,
    TTSSynthesizeRequest,
    TTSSynthesizeResponse,
    TTSVoice,
)

__all__ = [
    # Text schemas
    "TextFormatRequest",
    "TextFormatResponse",
    "TextCleanRequest",
    "TextCleanResponse",
    # TTS schemas
    "TTSEngine",
    "TTSVoice",
    "TTSParameter",
    "TTSEngineInfo",
    "TTSSynthesizeRequest",
    "TTSSynthesizeResponse",
]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟完整的启动过程来测试环境变量加载
"""

import os
import sys
from pathlib import Path

# 将src目录添加到路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))


def test_startup():
    """测试启动过程"""
    print("=== 模拟启动测试 ===")

    # 1. 检查初始环境变量
    print(f"1. 程序启动时 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")

    # 2. 模拟系统初始化
    print("2. 开始系统初始化...")

    # 直接导入并调用系统初始化
    from core.system_init import SystemInitializer

    initializer = SystemInitializer()

    # 分步执行初始化
    print("2.1 加载环境变量...")
    initializer._load_environment()

    print("2.2 设置日志...")
    initializer._setup_logging()

    print("2.3 初始化TTS管理器...")
    try:
        initializer._initialize_tts_manager()
        print("✓ TTS管理器初始化成功")

        # 查看可用引擎
        if initializer.tts_manager:
            engines = initializer.tts_manager.list_available_engines()
            print(f"可用的TTS引擎: {engines}")
    except Exception as e:
        print(f"✗ TTS管理器初始化失败: {e}")
        import traceback

        traceback.print_exc()

    print("2.4 验证组件...")
    try:
        initializer._validate_components()
        print("✓ 组件验证成功")
    except Exception as e:
        print(f"✗ 组件验证失败: {e}")

    # 标记为已初始化
    initializer._initialized = True
    print("✓ 系统初始化完成")


if __name__ == "__main__":
    test_startup()

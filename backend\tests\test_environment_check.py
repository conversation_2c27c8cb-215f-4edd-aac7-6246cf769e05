#!/usr/bin/env python3
import sys


def _is_test_environment() -> bool:
    """检测是否在测试环境中"""
    print(
        f"sys.modules keys: {[k for k in sys.modules.keys() if 'test' in k or 'pytest' in k]}"
    )
    print(f"sys.argv: {sys.argv}")

    condition1 = "pytest" in sys.modules
    condition2 = "unittest" in sys.modules
    condition3 = any("test" in arg for arg in sys.argv)

    print(f"pytest in sys.modules: {condition1}")
    print(f"unittest in sys.modules: {condition2}")
    print(f"test in sys.argv: {condition3}")

    result = condition1 or condition2 or condition3
    print(f"最终结果: {result}")

    return result


print("检查测试环境检测...")
is_test = _is_test_environment()
print(f"是否为测试环境: {is_test}")

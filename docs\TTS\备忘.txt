测试注册1
python -c "from src.tts.manager import create_tts_manager; manager = create_tts_manager(); print('Available engines:', manager.list_engines()); print('All models:', manager.list_all_models())"

测试注册2
python backend/tests/test_tts_dynamic_registration.py

测试注册3
python -m pytest tests/test_tts_dynamic_registration.py
cd backend && python -m pytest tests/test_tts_dynamic_registration.py -v

测试 test_edge_tts
D:/soft/Lang/miniconda3/envs/parrot/python.exe d:/Codes/AI/Novels/ParrotNovels/backend/tests/tts/test_edge_tts.py
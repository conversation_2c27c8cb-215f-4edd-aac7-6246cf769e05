#!/bin/bash

# =============================================================================
# ParrotNovels Dev Container 初始化脚本 (GPU 版本)
# =============================================================================

set -e  # 遇到错误立即退出

echo "🚀 开始初始化 ParrotNovels GPU 开发环境..."

# 设置 Python 路径
export PYTHONPATH=/workspace/backend/src

# =============================================================================
# 1. 验证 GPU 环境
# =============================================================================

echo "🔧 验证 GPU 环境..."
if [ -f "/usr/local/bin/verify-gpu.sh" ]; then
    bash /usr/local/bin/verify-gpu.sh
else
    echo "  ⚠️  GPU 验证脚本不存在，跳过验证"
fi

# =============================================================================
# 2. 创建必要的目录结构
# =============================================================================

echo "📁 创建项目目录结构..."

# 创建资源目录
mkdir -p /workspace/resources/{logs,storage,TTS}
mkdir -p /workspace/resources/storage/audio
mkdir -p /workspace/resources/TTS/{audio,edge-tts,index-tts,tmp}

# 创建日志目录
mkdir -p /workspace/resources/logs

# 设置权限
chmod -R 755 /workspace/resources

echo "  ✓ 目录结构创建完成"

# =============================================================================
# 3. 配置环境变量
# =============================================================================

echo "⚙️  配置环境变量..."

# 如果没有 .env 文件，从示例文件复制
if [ ! -f "/workspace/.env" ] && [ -f "/workspace/.env.example" ]; then
    echo "  ✓ 复制主环境变量配置文件..."
    cp /workspace/.env.example /workspace/.env
    
    # 设置开发环境特定的配置
    sed -i 's/API_HOST=127.0.0.1/API_HOST=0.0.0.0/' /workspace/.env
    sed -i 's/DEBUG=true/DEBUG=true/' /workspace/.env
    sed -i 's/FAST_STARTUP=false/FAST_STARTUP=true/' /workspace/.env
    
    # 添加 GPU 相关配置
    echo "# GPU 配置" >> /workspace/.env
    echo "INDEX_TTS_DEVICE=cuda" >> /workspace/.env
    echo "INDEX_TTS_USE_CUDA_KERNEL=true" >> /workspace/.env
    echo "CUDA_VISIBLE_DEVICES=all" >> /workspace/.env
    
    echo "  ✓ 主环境变量配置已更新为 GPU 容器环境"
fi

# =============================================================================
# 4. 安装依赖 (后台进行)
# =============================================================================

echo "📦 开始安装依赖 (后台进行)..."

# 创建后台安装脚本 - 使用中国镜像源
cat > /workspace/install-deps.sh << 'EOF'
#!/bin/bash

# 设置清华镜像源
PIP_INDEX="-i https://pypi.tuna.tsinghua.edu.cn/simple"

echo "安装 Python 后端依赖..."
if [ -f "/workspace/backend/requirements_llm.txt" ]; then
    pip install $PIP_INDEX -r /workspace/backend/requirements_llm.txt
fi

echo "安装开发工具依赖..."
pip install $PIP_INDEX fastapi uvicorn[standard] sqlalchemy pytest pytest-asyncio black isort pylint

echo "安装 index-tts 相关的额外依赖..."
pip install $PIP_INDEX transformers accelerate datasets scipy matplotlib seaborn

echo "安装前端依赖..."
if [ -f "/workspace/frontend/package.json" ]; then
    cd /workspace/frontend
    echo "使用 pnpm 安装前端依赖..."
    pnpm install
    cd /workspace
fi

echo "验证 GPU 环境..."
python -c "
import torch
print(f'PyTorch CUDA 可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU 设备数: {torch.cuda.device_count()}')
    print(f'当前 GPU: {torch.cuda.get_device_name(0)}')
    print(f'CUDA 版本: {torch.version.cuda}')
"

echo "依赖安装完成!"
EOF

chmod +x /workspace/install-deps.sh

# 后台运行安装脚本
nohup bash /workspace/install-deps.sh > /workspace/install.log 2>&1 &

echo "  ✓ 依赖安装已在后台启动，可查看 /workspace/install.log"

# =============================================================================
# 5. 创建启动脚本
# =============================================================================

echo "📝 创建启动脚本..."

# 设置可执行权限
chmod +x /workspace/.devcontainer/*.sh

echo "  ✓ 启动脚本已准备就绪"

# =============================================================================
# 6. 完成提示
# =============================================================================

echo ""
echo "🎉 ParrotNovels GPU 开发环境基础配置完成!"
echo ""
echo "🔧 GPU 相关命令:"
echo "  • 验证 GPU 环境: bash /usr/local/bin/verify-gpu.sh"
echo "  • 查看 GPU 状态: nvidia-smi"
echo "  • 查看 GPU 使用: gpustat"
echo ""
echo "📋 快速命令:"
echo "  • 启动后端:   bash /workspace/.devcontainer/start-backend.sh"
echo "  • 启动前端:   bash /workspace/.devcontainer/start-frontend.sh"
echo "  • 启动全部:   bash /workspace/.devcontainer/start-all.sh"
echo ""
echo "📋 手动完成安装 (如需要):"
echo "  • 检查安装进度: tail -f /workspace/install.log"
echo "  • 手动安装依赖: bash /workspace/install-deps.sh"
echo ""
echo "📁 重要目录:"
echo "  • 后端代码:   /workspace/backend/"
echo "  • 前端代码:   /workspace/frontend/"
echo "  • 资源文件:   /workspace/resources/"
echo ""
echo "💡 镜像源信息:"
echo "  • Python 包: 清华大学镜像源"
echo "  • npm 包: 淘宝镜像源"
echo "  • Ubuntu 包: 阿里云镜像源"
echo ""
echo "✨ 开发愉快! (GPU 加速已启用)"

cd /workspace 
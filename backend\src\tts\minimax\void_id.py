# -*- coding: utf-8 -*-
"""Minimax 音色查询模块

提供同步和异步方式查询 Minimax TTS 服务的音色信息。
"""

import logging
import os
from typing import Dict, List, Literal, Optional

import httpx

# 尝试使用项目的统一日志配置
try:
    from ...utils.logging_config import get_logger

    logger = get_logger(__name__)
except ImportError:
    # 如果导入失败，使用标准 logging
    logger = logging.getLogger(__name__)

# 类型定义
VoiceType = Literal["system", "voice_cloning", "voice_generation", "all"]


class Voice:
    """通用音色数据结构"""

    def __init__(
        self,
        voice_id: str,
        description: List[str],
        voice_name: Optional[str] = None,
        created_time: Optional[str] = None,
    ):
        self.voice_id = voice_id
        self.description = description
        self.voice_name = voice_name  # 仅系统音色有此字段
        self.created_time = created_time  # 仅生成类音色有此字段

    def __str__(self) -> str:
        """返回音色的字符串表示"""
        parts = [f"Voice(id={self.voice_id}"]

        if self.voice_name:
            parts.append(f"name={self.voice_name}")

        if self.description:
            desc_str = ", ".join(self.description[:2])  # 只显示前2个描述
            if len(self.description) > 2:
                desc_str += "..."
            parts.append(f"desc=[{desc_str}]")

        if self.created_time:
            parts.append(f"created={self.created_time}")

        return ", ".join(parts) + ")"

    def __repr__(self) -> str:
        """返回音色的详细表示"""
        return self.__str__()


class VoiceQueryResponse:
    """音色查询响应数据结构"""

    def __init__(
        self,
        system_voice: Optional[List[Voice]] = None,
        voice_cloning: Optional[List[Voice]] = None,
        voice_generation: Optional[List[Voice]] = None,
    ):
        self.system_voice = system_voice or []
        self.voice_cloning = voice_cloning or []
        self.voice_generation = voice_generation or []


class MinimaxVoiceQueryError(Exception):
    """Minimax 音色查询异常"""

    pass


def get_voices_sync(
    api_key: str,
    voice_type: VoiceType = "all",
    timeout: float = 30.0,
) -> VoiceQueryResponse:
    """同步查询 Minimax 音色信息

    Args:
        api_key: Minimax API 密钥
        voice_type: 音色类型，支持 "system", "voice_cloning",
                   "voice_generation", "all"
        timeout: 请求超时时间（秒）

    Returns:
        VoiceQueryResponse: 包含各类型音色信息的响应对象

    Raises:
        MinimaxVoiceQueryError: 当 API 请求失败或响应格式错误时

    Example:
        >>> response = get_voices_sync("your_api_key", "system")
        >>> for voice in response.system_voice:
        ...     print(f"音色ID: {voice.voice_id}, 名称: {voice.voice_name}")
    """
    url = "https://api.minimax.chat/v1/get_voice"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    data = {"voice_type": voice_type}

    try:
        with httpx.Client(timeout=timeout) as client:
            response = client.post(url, headers=headers, json=data)
            response.raise_for_status()

        result_data = response.json()
        logger.info(f"Successfully retrieved voice info, type: {voice_type}")

        return _parse_voice_response(result_data)

    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP request failed: {e.response.status_code}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(
            f"HTTP request failed: {e.response.status_code}"
        ) from e
    except httpx.RequestError as e:
        error_msg = f"Request error: {type(e).__name__}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(f"Request error: {type(e).__name__}") from e
    except Exception as e:
        error_msg = f"Parse response data failed: {type(e).__name__}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(
            f"Parse response data failed: {type(e).__name__}"
        ) from e


async def get_voices_async(
    api_key: str,
    voice_type: VoiceType = "all",
    timeout: float = 30.0,
) -> VoiceQueryResponse:
    """异步查询 Minimax 音色信息

    Args:
        api_key: Minimax API 密钥
        voice_type: 音色类型，支持 "system", "voice_cloning",
                   "voice_generation", "all"
        timeout: 请求超时时间（秒）

    Returns:
        VoiceQueryResponse: 包含各类型音色信息的响应对象

    Raises:
        MinimaxVoiceQueryError: 当 API 请求失败或响应格式错误时

    Example:
        >>> import asyncio
        >>> async def main():
        ...     response = await get_voices_async("your_api_key", "system")
        ...     for voice in response.system_voice:
        ...         print(f"音色ID: {voice.voice_id}, 名称: {voice.voice_name}")
        >>> asyncio.run(main())
    """
    url = "https://api.minimax.chat/v1/get_voice"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    data = {"voice_type": voice_type}

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()

        result_data = response.json()
        logger.info(f"Successfully retrieved voice info, type: {voice_type}")

        return _parse_voice_response(result_data)

    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP request failed: {e.response.status_code}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(
            f"HTTP request failed: {e.response.status_code}"
        ) from e
    except httpx.RequestError as e:
        error_msg = f"Request error: {type(e).__name__}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(f"Request error: {type(e).__name__}") from e
    except Exception as e:
        error_msg = f"Parse response data failed: {type(e).__name__}"
        logger.error(error_msg)
        raise MinimaxVoiceQueryError(
            f"Parse response data failed: {type(e).__name__}"
        ) from e


def _parse_voice_response(data: Dict) -> VoiceQueryResponse:
    """解析音色查询响应数据

    Args:
        data: API 响应的 JSON 数据

    Returns:
        VoiceQueryResponse: 解析后的响应对象
    """
    try:
        # 检查数据类型
        if not isinstance(data, dict):
            logger.error(f"Expected dict, got {type(data)}")
            raise TypeError(f"Expected dict, got {type(data)}")

        def _parse_voice_list(
            voice_list_data: List[Dict], voice_type: str
        ) -> List[Voice]:
            """通用音色列表解析函数"""
            voices = []
            if isinstance(voice_list_data, list):
                for voice_data in voice_list_data:
                    if isinstance(voice_data, dict):
                        # 提取通用字段
                        voice_id = str(voice_data.get("voice_id", ""))
                        description = (
                            voice_data.get("description", [])
                            if isinstance(voice_data.get("description"), list)
                            else []
                        )

                        # 根据音色类型提取特定字段
                        voice_name = None
                        created_time = None

                        if voice_type == "system":
                            voice_name = str(voice_data.get("voice_name", ""))
                        else:  # voice_cloning 或 voice_generation
                            created_time = str(voice_data.get("created_time", ""))

                        voices.append(
                            Voice(
                                voice_id=voice_id,
                                description=description,
                                voice_name=voice_name,
                                created_time=created_time,
                            )
                        )
            return voices

        # 解析各类型音色
        system_voices = []
        if "system_voice" in data and data["system_voice"] is not None:
            system_voices = _parse_voice_list(data["system_voice"], "system")

        voice_cloning = []
        if "voice_cloning" in data and data["voice_cloning"] is not None:
            voice_cloning = _parse_voice_list(data["voice_cloning"], "voice_cloning")

        voice_generation = []
        if "voice_generation" in data and data["voice_generation"] is not None:
            voice_generation = _parse_voice_list(
                data["voice_generation"], "voice_generation"
            )

        return VoiceQueryResponse(
            system_voice=system_voices,
            voice_cloning=voice_cloning,
            voice_generation=voice_generation,
        )
    except Exception as e:
        logger.error(f"Error parsing voice response: {e}, data type: {type(data)}")
        raise TypeError(f"Error parsing voice response: {e}") from e


# 示例用法
if __name__ == "__main__":
    import sys

    from dotenv import load_dotenv

    # 加载环境变量 - 查找项目根目录的 .env 文件
    # 从当前文件位置向上查找项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = current_dir

    # 向上查找直到找到包含 .env 文件的目录或到达根目录
    env_loaded = False
    while project_root != os.path.dirname(project_root):
        env_file = os.path.join(project_root, ".env")
        if os.path.exists(env_file):
            print(f"Found .env file at: {env_file}")
            # 显示文件内容进行调试
            try:
                with open(env_file, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    print("Content of .env file:")
                    for i, line in enumerate(lines[:10], 1):  # 只显示前10行
                        if "MINIMAX_API_KEY" in line:
                            print(f"  Line {i}: {line.strip()}")
            except Exception as e:
                print(f"Error reading .env file: {e}")

            # 只加载指定的 .env 文件，不加载 .env.example
            load_dotenv(env_file, override=True)
            env_loaded = True
            break
        project_root = os.path.dirname(project_root)

    if not env_loaded:
        print("No .env file found in project directories")

    # 从环境变量获取 API key
    API_KEY = os.getenv("MINIMAX_API_KEY")
    # 调试：显示所有以 MINIMAX 开头的环境变量
    minimax_vars = {k: v for k, v in os.environ.items() if k.startswith("MINIMAX")}
    print("MINIMAX environment variables: ", minimax_vars)

    if not API_KEY:
        print("Error: MINIMAX_API_KEY not found in environment variables.")
        print("Please create a .env file with: MINIMAX_API_KEY=your_actual_api_key")
        sys.exit(1)

    print("Testing Minimax Voice Query API...")

    # 同步调用示例
    try:
        response = get_voices_sync(API_KEY, "system")
        print(f"\nSystem voices count: {len(response.system_voice)}")
        print("First 3 system voices:")
        for voice in response.system_voice:  # 只显示前3个
            print(voice)
            # print(f"  - {voice.voice_name} ({voice.voice_id})")
    except MinimaxVoiceQueryError as e:
        print(f"Sync query failed: {e}")

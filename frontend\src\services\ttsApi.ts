// TTS API服务
import type {
  TTSParametersConfig,
  TTSParameterValues,
  TTSEngine,
} from '../types/tts';

// TTS引擎信息接口（向后兼容）
export interface TTSEngineInfo {
  id: string;
  name: string;
  description: string;
  version: string;
  is_available: boolean;
  supported_languages: string[];
  requires_reference: boolean;
  supports_streaming: boolean;
}

// API响应接口
export interface TTSParametersResponse {
  parameters: TTSParametersConfig;
}

export interface TTSModelsResponse {
  models: string[];
}

export interface TTSSynthesizeRequest {
  engine_type: string;
  voice_id_name: string;
  voice_style_name: string;
  text: string;
  output_path?: string;
  parameters?: TTSParameterValues;
}

export interface TTSSynthesizeResponse {
  success: boolean;
  message?: string;
  audio_url?: string; // 音频文件的访问URL
  audio_file_path?: string; // 服务器上的音频文件路径
  duration?: number; // 音频时长（秒）
  file_size?: number; // 文件大小（字节）
  format?: string; // 音频格式（如 "wav", "mp3"）
  sample_rate?: number; // 采样率
}

class TTSApiService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取可用的TTS引擎列表
   */
  async getAvailableEngines(): Promise<TTSEngine[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tts/engines`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // 处理数组格式的响应（如engine.json的格式）
      if (Array.isArray(data)) {
        return data as TTSEngine[];
      }

      // 处理对象格式的响应
      if (data.engines && Array.isArray(data.engines)) {
        return data.engines as TTSEngine[];
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('获取TTS引擎列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取指定引擎的参数配置
   */
  async getEngineParameters(engineType: string): Promise<TTSParametersConfig> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/tts/engines/${engineType}/parameters`
      );
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`引擎 ${engineType} 不存在`);
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: TTSParametersResponse = await response.json();
      return data.parameters;
    } catch (error) {
      console.error(`获取引擎 ${engineType} 参数失败:`, error);
      throw error;
    }
  }

  /**
   * 获取指定引擎的模型列表
   */
  async getEngineModels(engineType: string): Promise<string[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/tts/engines/${engineType}/models`
      );
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`引擎 ${engineType} 不存在`);
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: TTSModelsResponse = await response.json();
      return data.models;
    } catch (error) {
      console.error(`获取引擎 ${engineType} 模型失败:`, error);
      throw error;
    }
  }

  /**
   * 合成语音
   */
  async synthesizeText(
    request: TTSSynthesizeRequest
  ): Promise<TTSSynthesizeResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tts/synthesize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('语音合成失败:', error);
      throw error;
    }
  }

  /**
   * 获取引擎状态
   */
  async getEngineStatus(engineType: string): Promise<{
    available: boolean;
    error?: string;
    engine_info?: any;
  }> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/tts/engines/${engineType}/status`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`获取引擎 ${engineType} 状态失败:`, error);
      return {
        available: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 验证参数配置
   */
  validateParameters(
    parameters: TTSParameterValues,
    config: TTSParametersConfig
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    for (const [paramName, value] of Object.entries(parameters)) {
      const paramConfig = config[paramName];
      if (!paramConfig) {
        errors.push(`未知参数: ${paramName}`);
        continue;
      }

      // 类型验证
      switch (paramConfig.type) {
        case 'int':
          if (!Number.isInteger(value)) {
            errors.push(`参数 ${paramName} 必须是整数`);
          }
          break;
        case 'float':
          if (typeof value !== 'number') {
            errors.push(`参数 ${paramName} 必须是数字`);
          }
          break;
        case 'bool':
          if (typeof value !== 'boolean') {
            errors.push(`参数 ${paramName} 必须是布尔值`);
          }
          break;
        case 'str':
        case 'select':
          if (typeof value !== 'string') {
            errors.push(`参数 ${paramName} 必须是字符串`);
          }
          break;
      }

      // 范围验证
      if (paramConfig.range) {
        if (paramConfig.type === 'int' || paramConfig.type === 'float') {
          const [min, max] = paramConfig.range as number[];
          if (value < min || value > max) {
            errors.push(`参数 ${paramName} 必须在 ${min} 到 ${max} 之间`);
          }
        } else if (
          paramConfig.type === 'select' ||
          paramConfig.type === 'str'
        ) {
          const options = paramConfig.range as string[];
          if (!options.includes(value)) {
            errors.push(
              `参数 ${paramName} 必须是以下选项之一: ${options.join(', ')}`
            );
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 创建默认实例
export const ttsApi = new TTSApiService();

// 导出类供其他地方使用
export default TTSApiService;

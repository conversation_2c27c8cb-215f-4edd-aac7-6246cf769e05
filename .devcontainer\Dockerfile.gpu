# 使用 NVIDIA CUDA 12.9 + cuDNN 运行时镜像
FROM nvidia/cuda:12.9.0-cudnn-runtime-ubuntu22.04

# 构建参数
ARG PYTHON_VERSION=3.12
ARG NODE_VERSION=22

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# CUDA 相关环境变量
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 设置工作目录
WORKDIR /workspace

# 配置中国镜像源
RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    echo "# 阿里云镜像源" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list

# 更新系统并安装基础依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    curl \
    wget \
    git \
    unzip \
    zip \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    # 音频处理依赖
    ffmpeg \
    libsndfile1 \
    # Python 开发
    python3 \
    python3-dev \
    python3-pip \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

# 创建 python 符号链接
RUN ln -s /usr/bin/python3 /usr/bin/python

# 配置 pip 使用阿里云镜像源（如果不可用会自动降级到官方源）
RUN mkdir -p /root/.pip && \
    echo "[global]" > /root/.pip/pip.conf && \
    echo "timeout = 60" >> /root/.pip/pip.conf && \
    echo "retries = 3" >> /root/.pip/pip.conf

# 安装 Node.js 并配置 npm 镜像
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && npm config set registry https://registry.npmmirror.com

# 安装 pnpm 并配置镜像
RUN npm install -g pnpm@latest \
    && pnpm config set registry https://registry.npmmirror.com

# 升级 pip
RUN python3 -m pip install --upgrade pip setuptools wheel

# 安装 PyTorch（CUDA 12.1 兼容版本，适配 CUDA 12.9）
RUN pip install torch torchvision torchaudio

# 安装基础开发包
RUN pip install \
    fastapi \
    uvicorn[standard] \
    sqlalchemy \
    pytest \
    black \
    isort \
    # 音频处理
    librosa \
    soundfile \
    pydub \
    # GPU 工具
    gpustat \
    nvidia-ml-py3 \
    # 常用包
    numpy \
    pandas \
    requests \
    python-dotenv \
    pydantic

# 创建 GPU 验证脚本
RUN echo '#!/bin/bash\n\
echo "🔧 GPU 环境验证脚本"\n\
echo "=================="\n\
echo "1. 检查 NVIDIA 驱动..."\n\
if command -v nvidia-smi &> /dev/null; then\n\
    nvidia-smi\n\
    echo "✅ NVIDIA 驱动可用"\n\
else\n\
    echo "❌ NVIDIA 驱动不可用"\n\
    exit 1\n\
fi\n\
echo ""\n\
echo "2. 检查 CUDA 安装..."\n\
if command -v nvcc &> /dev/null; then\n\
    nvcc --version\n\
    echo "✅ CUDA 编译器可用"\n\
else\n\
    echo "⚠️  CUDA 编译器不可用（运行时镜像正常）"\n\
fi\n\
echo ""\n\
echo "3. 检查 PyTorch GPU 支持..."\n\
python -c "\n\
import torch\n\
print(f\"PyTorch 版本: {torch.__version__}\")\n\
print(f\"CUDA 可用: {torch.cuda.is_available()}\")\n\
if torch.cuda.is_available():\n\
    print(f\"CUDA 版本: {torch.version.cuda}\")\n\
    print(f\"GPU 数量: {torch.cuda.device_count()}\")\n\
    for i in range(torch.cuda.device_count()):\n\
        print(f\"GPU {i}: {torch.cuda.get_device_name(i)}\")\n\
else:\n\
    print(\"⚠️  PyTorch 无法访问 GPU\")\n\
"\n\
echo ""\n\
echo "4. 检查 index-tts 相关依赖..."\n\
python -c "\n\
try:\n\
    import librosa\n\
    print(\"✅ librosa 可用\")\n\
except ImportError:\n\
    print(\"❌ librosa 不可用\")\n\
\n\
try:\n\
    import soundfile\n\
    print(\"✅ soundfile 可用\")\n\
except ImportError:\n\
    print(\"❌ soundfile 不可用\")\n\
"\n\
echo ""\n\
echo "✅ GPU 环境验证完成!"\n\
' > /usr/local/bin/verify-gpu.sh && chmod +x /usr/local/bin/verify-gpu.sh

# 设置环境变量
ENV PYTHONPATH=/workspace/backend/src

# 暴露端口
EXPOSE 8000 5173 2024 8888

# 默认命令
CMD ["/bin/bash"] 
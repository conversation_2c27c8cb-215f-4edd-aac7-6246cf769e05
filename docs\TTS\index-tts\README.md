# IndexTTS 实现

基于 [index-tts](https://github.com/index-tts/index-tts) 项目的零样本语音合成实现。

## 功能特性

- **零样本语音合成**：无需训练即可使用任意参考语音进行合成
- **多语言支持**：支持中文、英文及中英文混合文本
- **拼音标注**：支持拼音标注以控制发音
- **流式合成**：支持实时流式音频生成
- **灵活配置**：支持多种质量和性能配置
- **统一接口**：遵循 TTSBase 抽象基类规范

## 安装依赖

### 1. 安装 IndexTTS

```bash
# 克隆 IndexTTS 项目
git clone https://github.com/index-tts/index-tts.git
cd index-tts

# 安装依赖
pip install ninja
pip install -e . --no-deps --no-build-isolation
```

### 2. 下载模型

根据 IndexTTS 文档下载所需的模型文件：

```bash
huggingface-cli download IndexTeam/IndexTTS-1.5 \
  config.yaml bigvgan_discriminator.pth bigvgan_generator.pth bpe.model dvae.pth gpt.pth unigram_12000.vocab \
  --local-dir checkpoints
```

## 配置说明

### 配置参数说明

- `model_path`: 模型文件路径
- `config_path`: 配置文件路径
- `precision`: 精度设置（"fp16" 或 "fp32"）
- `device`: 设备类型（"cpu" 或 "cuda"）
- `use_cuda_kernel`: 是否使用 CUDA 内核
- `reference_voice_path`: 默认参考语音路径

## 使用方法

### 基本使用

```python
from index_tts import IndexTTS
from config_example import get_config

# 初始化
config = get_config("default")
tts = IndexTTS(config)

# 合成语音
audio_data = tts.synthesize(
    text="Hello, 这是 IndexTTS 的测试。",
    reference_voice="path/to/reference.wav",
    output_path="output.wav"
)
```

### 流式合成

```python
# 流式合成
for chunk in tts.synthesize_stream(
    text="这是流式合成的示例",
    reference_voice="path/to/reference.wav"
):
    # 处理音频块
    process_audio_chunk(chunk)
```

### 中英文混合

```python
# 中英文混合文本
mixed_text = "Welcome to IndexTTS, 这是一个支持中英文的TTS系统。"
audio_data = tts.synthesize(
    text=mixed_text,
    reference_voice="path/to/reference.wav"
)
```

### 拼音标注

```python
# 使用拼音标注控制发音
pinyin_text = "这个字'银行'的'行'读hang2，不读xing2"
audio_data = tts.synthesize(
    text=pinyin_text,
    reference_voice="path/to/reference.wav"
)
```

## API 参考

### IndexTTS 类

#### 初始化

```python
IndexTTS(config: Dict[str, Any])
```

#### 主要方法

- `synthesize(text, reference_voice, output_path=None, **kwargs)`: 合成语音
- `synthesize_stream(text, reference_voice, **kwargs)`: 流式合成
- `get_voices(languages=None, regions=None, gender=None)`: 获取语音列表
- `get_models()`: 获取模型列表
- `set_reference_voice(voice_path)`: 设置参考语音
- `validate_reference_voice(voice_path)`: 验证参考语音
- `get_supported_languages()`: 获取支持的语言
- `get_engine_info()`: 获取引擎信息

### 参数说明

#### synthesize 方法参数

- `text` (str): 要合成的文本
- `reference_voice` (str): 参考语音文件路径
- `output_path` (str, 可选): 输出音频文件路径
- `speed` (float, 可选): 语速控制，默认 1.0
- `pitch` (float, 可选): 音调控制，默认 1.0
- `volume` (float, 可选): 音量控制，默认 1.0

#### synthesize_stream 方法参数

- `text` (str): 要合成的文本
- `reference_voice` (str): 参考语音文件路径
- `chunk_size` (int, 可选): 音频块大小，默认 1024
- 其他参数同 `synthesize` 方法

## 示例代码

运行 `example_usage.py` 查看完整的使用示例：

```bash
python example_usage.py
```

示例包括：

1. **基本使用示例**：简单的文本到语音合成
2. **流式合成示例**：实时音频生成
3. **中英文混合示例**：多语言文本处理
4. **拼音标注示例**：发音控制
5. **语音信息示例**：获取系统信息
6. **参考语音管理示例**：语音文件验证和管理
7. **性能对比示例**：不同配置的性能测试

## 性能优化

### 配置选择

- **快速模式**：使用 FP16 精度，适合实时应用
- **高质量模式**：使用 FP32 精度，适合离线处理
- **GPU 加速**：使用 CUDA 设备提升性能

### 最佳实践

1. **参考语音选择**：
   - 使用清晰、无噪音的音频文件
   - 推荐时长 3-10 秒
   - 支持 WAV、MP3、FLAC 格式

2. **文本预处理**：
   - 使用标准标点符号
   - 对于多音字使用拼音标注
   - 避免过长的句子

3. **内存管理**：
   - 对于长文本使用流式合成
   - 及时释放不需要的音频数据
   - 合理设置批处理大小

## 错误处理

常见错误及解决方案：

### 1. 模型加载失败

```python
try:
    tts = IndexTTS(config)
except Exception as e:
    print(f"模型加载失败: {e}")
    # 检查模型路径和配置文件
```

### 2. 参考语音无效

```python
if not tts.validate_reference_voice(voice_path):
    print(f"无效的参考语音文件: {voice_path}")
    # 检查文件格式和路径
```

### 3. CUDA 内存不足

```python
# 使用 CPU 模式
INDEX_TTS_DEVICE = cpu
# 或降低精度
INDEX_TTS_PRECISION = fp16
```

## 注意事项

1. **依赖要求**：确保已正确安装 IndexTTS 及其依赖
2. **模型文件**：确保模型文件路径正确且可访问
3. **音频格式**：参考语音文件应为支持的音频格式
4. **内存使用**：长文本合成可能需要大量内存
5. **GPU 支持**：CUDA 功能需要兼容的 GPU 和驱动

## 许可证

本实现遵循 IndexTTS 项目的许可证要求。

## 相关链接

- [IndexTTS 官方项目](https://github.com/index-tts/index-tts)
- [IndexTTS 文档](https://index-tts.github.io/)
- [TTSBase 抽象基类](../tts_base.py)
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

try:
    from src.tts.engine import TTSEngineType
except ImportError:
    from tts.engine import TTSEngineType


class TTSEngine(BaseModel):
    """TTS引擎信息"""

    id: str
    name: str
    description: str
    is_available: bool


class TTSVoice(BaseModel):
    """TTS声音信息

    基于 meta.json 的数据结构定义，包含完整的声音元数据信息
    """

    id_name: str  # 声音唯一标识，如 "zh-CN-YunxiNeural", "zh-CN-害羞少女"
    gender: str  # 性别，"Male" 或 "Female"
    display_name: str  # 显示名称，如 "云希（男）-中文-内地"
    locale: str  # 地区标识，如 "zh-CN", "zh-TW"
    age_group: str  # 年龄组，如 "青年", "中年", "少女"
    description: str  # 声音特征描述，详细说明声音风格和适用场景
    styles: Dict[str, str]  # 声音风格，如 {"通用": "zh-CN-Yunxi-General-Audio.wav"}


class TTSParameter(BaseModel):
    """TTS参数配置定义"""

    type: str  # 'int', 'float', 'str', 'bool', 'select'
    control: str  # 'slider', 'switch', 'select', 'input'
    label: str
    range: Optional[List[Any]] = None
    default: Any
    description: str
    unit: Optional[str] = None
    step: Optional[float] = None
    category: Optional[str] = None
    depends_on: Optional[Dict[str, Any]] = None
    advanced: Optional[bool] = False


class TTSEngineInfo(BaseModel):
    """TTS引擎详细信息"""

    id: str
    name: str
    version: str
    description: str
    type: str
    models: List[str]
    is_available: bool
    supported_languages: List[str]
    supports_streaming: bool = False
    parameters: Dict[str, TTSParameter]
    voices: List[TTSVoice]


class TTSSynthesizeRequest(BaseModel):
    """TTS合成请求"""

    engine_type: TTSEngineType = Field(..., description="TTS引擎类型", example="index")
    voice_id_name: str = Field(..., description="声音ID名称", example="zh-CN-Yunxi")
    voice_style_name: str = Field(..., description="声音风格名称", example="助手")
    text: str = Field(
        ...,
        description="要合成的文本",
        example="阳光穿过枝叶的缝隙，在草地上织出一片碎金。风裹着蒲公英的绒毛掠过肩头，带来远处孩童的笑声。",
    )
    output_path: Optional[str] = Field(
        None, description="输出文件路径", example="a.wav"
    )
    parameters: Optional[Dict[str, Any]] = Field(
        None, description="额外参数", example={}
    )


class TTSSynthesizeResponse(BaseModel):
    """TTS合成响应"""

    success: bool
    message: Optional[str] = None
    audio_url: Optional[str] = None  # 音频文件的访问URL
    audio_file_path: Optional[str] = None  # 服务器上的音频文件路径
    duration: Optional[float] = None  # 音频时长（秒）
    file_size: Optional[int] = None  # 文件大小（字节）
    format: Optional[str] = None  # 音频格式（如 "wav", "mp3"）
    sample_rate: Optional[int] = None  # 采样率

"""质量控制模块

包含质量检查、决策控制等相关功能
"""

from datetime import datetime

from ...utils.logging_config import get_logger
from .langsmith_config import log_workflow_metrics, with_langsmith_tracing

logger = get_logger(__name__)


class QualityController:
    """质量控制器"""

    def __init__(self, workflow_instance):
        """
        初始化质量控制器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    @with_langsmith_tracing(
        run_name="quality_check",
        tags=["quality_control", "validation"],
        exclude_fields=[
            "novel_content",
            "chapter_contents",
            "overall_analysis",
            "characters",
            "chapters",
            "annotated_chapters",
        ],
    )
    def quality_check(self, state):
        """质量检查节点"""
        logger.info(f"{state['workflow_id']}: 开始质量检查")

        try:
            quality_scores = {}

            # 1. 角色分析与对话提取对比
            original_characters = state.get("original_characters", [])
            refined_characters = state["characters"]
            original_count = len(original_characters)
            character_count = len(refined_characters)

            if original_count > 0:
                character_retention_rate = character_count / original_count
                quality_scores["character_retention_rate"] = character_retention_rate

                logger.info(
                    f"{state['workflow_id']}: 角色数量对比: "
                    f"原始分析 {original_count} 个 → 实际对话 {character_count} 个 "
                    f"(保留率: {character_retention_rate:.2%})"
                )
            else:
                quality_scores["character_retention_rate"] = 0
                logger.warning(f"{state['workflow_id']}: 未找到原始角色数据")

            # 2. 文本内容完整性对比（中英文字符数）
            content_integrity_score = self._check_content_integrity(state)
            quality_scores["content_integrity"] = content_integrity_score

            # 计算总体质量分数
            overall_score = sum(quality_scores.values()) / len(quality_scores)
            quality_scores["overall"] = overall_score

            # 记录质量检查指标
            log_workflow_metrics(
                state,
                "quality_check",
                {
                    "original_characters_count": original_count,
                    "refined_characters_count": character_count,
                    "character_retention_rate": character_retention_rate,
                    "content_integrity_score": content_integrity_score,
                    "overall_score": overall_score,
                },
                exclude_fields=[
                    "novel_content",
                    "chapter_contents",
                    "overall_analysis",
                    "characters",
                    "chapters",
                    "annotated_chapters",
                ],
            )

            logger.info(f"{state['workflow_id']}: 质量分数: {overall_score:.2f}")

            return {
                **state,
                "step": "decide_after_quality_check",
                "quality_scores": quality_scores,
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.9,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    def _check_content_integrity(self, state):
        """检查文本内容完整性 - 对比原文和对话提取的中英文字符数"""
        try:
            # 获取原始小说内容
            novel_content = state.get("novel_content", "")

            # 获取所有对话标注的内容
            annotated_chapters = state.get("annotated_chapters", [])
            dialogue_content = ""

            for chapter in annotated_chapters:
                dialogue_annotations = chapter.get("dialogue_annotations", [])
                for annotation in dialogue_annotations:
                    if isinstance(annotation, dict):
                        content = annotation.get("content", "")
                        dialogue_content += content

                        # 统计有意义字符数
            original_char_count = self._count_chinese_english_chars(novel_content)
            dialogue_char_count = self._count_chinese_english_chars(dialogue_content)

            # 计算保留率
            if original_char_count == 0:
                retention_rate = 0
                integrity_score = 0
            else:
                retention_rate = dialogue_char_count / original_char_count

                # 调整误差范围：75%-105%（有意义字符统计更严格，容错范围适当缩小）
                if 0.75 <= retention_rate <= 1.05:
                    # 在合理范围内，根据接近程度给分
                    if 0.90 <= retention_rate <= 1.0:
                        integrity_score = 1.0  # 完美范围：90%-100%
                    elif 0.80 <= retention_rate <= 1.02:
                        integrity_score = 0.8  # 良好范围：80%-102%
                    else:
                        integrity_score = 0.6  # 可接受范围：75%-105%
                else:
                    integrity_score = 0.3  # 超出合理范围

            # 记录详细信息
            logger.info(
                f"{state['workflow_id']}: 文本完整性检查: "
                f"原文有意义字符 {original_char_count} 个 → "
                f"对话提取有意义字符 {dialogue_char_count} 个 "
                f"(保留率: {retention_rate:.2%}, 完整性评分: {integrity_score:.2f})"
            )

            return integrity_score

        except Exception as e:
            logger.error(f"{state['workflow_id']}: 文本完整性检查失败: {e}")
            return 0.5  # 检查失败时给予中等分数

    def _count_chinese_english_chars(self, text):
        """统计文本中的有意义字符数量（中英文+数字+常用标点）

        这种方法更适合TTS质量评估，因为：
        1. 包含标点符号（影响语调、停顿）
        2. 包含数字（重要的朗读内容）
        3. 排除纯空白和特殊符号
        4. 更准确地反映对话提取的完整性
        """
        import re

        if not text:
            return 0

        # 匹配有意义的字符：中文、英文、数字、常用标点符号
        # 中文字符：\u4e00-\u9fff (CJK统一汉字)
        # 英文字母：a-zA-Z
        # 数字：0-9
        # 常用标点：。！？，、；：""''（）《》【】…—·等
        meaningful_pattern = (
            r"[\u4e00-\u9fff]|[a-zA-Z]|[0-9]|[。！？，、；：" "''（）《》【】…—·]"
        )

        matches = re.findall(meaningful_pattern, text)
        total_count = len(matches)

        logger.debug(f"有意义字符统计: 总计 {total_count} 个字符")

        return total_count

    def decide_after_quality_check(self, state) -> str:
        """质量检查后的决策"""
        quality_scores = state["quality_scores"]
        overall_score = quality_scores.get("overall", 0)
        character_retention_rate = quality_scores.get("character_retention_rate", 0)
        content_integrity = quality_scores.get("content_integrity", 0)
        retry_count = state["retry_count"]
        max_retries = 2

        # 新的质量判断标准：
        # 1. 角色保留率 >= 0.3 (至少保留30%的角色)
        # 2. 内容完整性 >= 0.6 (内容基本完整)
        # 3. 总体评分 >= 0.6
        character_ok = character_retention_rate >= 0.3
        content_ok = content_integrity >= 0.6
        overall_ok = overall_score >= 0.6

        logger.info(
            f"{state['workflow_id']}: 质量检查决策: "
            f"角色保留率 {character_retention_rate:.2%} ({'✓' if character_ok else '✗'}), "
            f"内容完整性 {content_integrity:.2f} ({'✓' if content_ok else '✗'}), "
            f"总体评分 {overall_score:.2f} ({'✓' if overall_ok else '✗'})"
        )

        if character_ok and content_ok and overall_ok:
            return "finalize"
        elif retry_count < max_retries:
            logger.info(
                f"{state['workflow_id']}: 质量检查未通过，准备第 {retry_count + 1} 次重试"
            )
            return "retry"
        else:
            logger.warning(f"{state['workflow_id']}: 已达到最大重试次数，处理失败")
            return "failed"

    def retry_processing(self, state):
        """重试处理节点"""
        retry_count = state["retry_count"] + 1
        logger.info(f"{state['workflow_id']}: 开始第 {retry_count} 次重试")

        return {
            **state,
            "step": "annotate_dialogues",
            "retry_count": retry_count,
            "updated_at": datetime.now().isoformat(),
        }

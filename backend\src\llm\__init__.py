"""
LLM模块 - 大语言模型相关功能

包含LangGraph工作流和liteLLM统一调用接口
"""

# 延迟导入，避免相对导入问题
__all__ = ["LLMManager", "LLMClient", "LLMConfig"]


def get_llm_client():
    """获取LLM客户端"""
    from .client import LLMClient

    return LLMClient


def get_llm_config():
    """获取LLM配置"""
    from .config import LLMConfig

    return LLMConfig


def get_llm_manager():
    """获取LLM管理器"""
    from .manager import LLMManager

    return LLMManager

from pathlib import Path
from typing import List, Optional, Tuple

from fastapi import HTTPException

from ..db.voice_meta import VoiceMetadata, VoiceMetaDB
from ..schemas.tts_schema import TTSVoice
from ..tts.engine import TTSEngineType
from ..utils.logging_config import get_logger
from ..utils.path_utils import resolve_resource_path

logger = get_logger(__name__)


class VoiceService:
    """声音文件服务"""

    def __init__(self):
        self.voice_base_dir = resolve_resource_path("TTS")
        self.allowed_extensions = {".wav", ".mp3", ".ogg", ".m4a", ".aac"}
        self.mime_type_map = {
            ".wav": "audio/wav",
            ".mp3": "audio/mpeg",
            ".ogg": "audio/ogg",
            ".m4a": "audio/mp4",
            ".aac": "audio/aac",
        }
        self.voice_db = VoiceMetaDB()

    def get_voice_file_info(self, voice_path: str) -> Tuple[Path, str]:
        """
        获取声音文件信息

        Args:
            voice_path: 声音文件路径，如 "audio/YunXi/zh-CN-YunXi-General.wav"

        Returns:
            Tuple[Path, str]: 文件路径和MIME类型

        Raises:
            HTTPException: 当文件不存在或格式不支持时
        """
        try:
            # 构建完整的文件路径
            file_path = self.voice_base_dir / voice_path

            logger.debug(f"查找声音文件: {file_path}")

            # 检查文件是否存在
            if not file_path.exists():
                # 尝试大小写不敏感的搜索
                file_path = self._find_case_insensitive_file(voice_path)
                if not file_path:
                    logger.warning(f"声音文件不存在: {voice_path}")
                    raise HTTPException(
                        status_code=404, detail=f"声音文件不存在: {voice_path}"
                    )
            else:
                logger.debug(f"找到声音文件: {file_path}")

            # 检查文件扩展名
            if file_path.suffix.lower() not in self.allowed_extensions:
                logger.warning(f"不支持的文件格式: {file_path.suffix}")
                raise HTTPException(
                    status_code=400, detail=f"不支持的文件格式: {file_path.suffix}"
                )

            # 获取MIME类型
            file_ext = file_path.suffix.lower()
            media_type = self.mime_type_map.get(file_ext, "audio/wav")

            return file_path, media_type

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取声音文件失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取声音文件失败: {str(e)}")

    def _find_case_insensitive_file(self, voice_path: str) -> Optional[Path]:
        """
        大小写不敏感的文件搜索

        Args:
            voice_path: 声音文件路径

        Returns:
            Optional[Path]: 找到的文件路径，如果没找到返回None
        """
        logger.debug(f"尝试大小写不敏感搜索文件: {voice_path}")
        try:
            # 分割路径为目录和文件名
            path_parts = voice_path.split("/")
            if len(path_parts) < 2:
                logger.debug(f"路径格式不正确，无法进行大小写不敏感搜索: {voice_path}")
                return None

            dir_name = path_parts[0]
            file_name = "/".join(path_parts[1:])

            # 在voices目录下查找匹配的目录（大小写不敏感）
            for item in self.voice_base_dir.iterdir():
                if item.is_dir() and item.name.lower() == dir_name.lower():
                    alternative_path = item / file_name
                    if alternative_path.exists():
                        logger.info(f"找到大小写不匹配的文件: {alternative_path}")
                        return alternative_path

            logger.debug(f"未找到大小写不敏感的文件: {voice_path}")
            return None

        except Exception as e:
            logger.error(f"搜索文件时出错: {e}")
            return None

    def _convert_voice_metadata_to_tts_voice(
        self, voice_meta: VoiceMetadata
    ) -> TTSVoice:
        """将数据库模型转换为 TTSVoice schema

        Args:
            voice_meta: 数据库语音元数据

        Returns:
            TTSVoice: TTS语音对象
        """
        return TTSVoice(
            id_name=voice_meta.id_name,
            gender=voice_meta.gender,
            display_name=voice_meta.display_name,
            locale=voice_meta.locale,
            age_group=voice_meta.age_group,
            description=voice_meta.description,
            styles=voice_meta.styles,
        )

    def get_voices(
        self,
        engine_type: Optional[TTSEngineType] = None,
        language: Optional[str] = None,
        gender: Optional[str] = None,
        age_group: Optional[str] = None,
    ) -> List[TTSVoice]:
        """获取语音列表，从数据库获取数据

        Args:
            engine_type: TTS引擎类型过滤，如 TTSEngineType.EDGE_TTS, TTSEngineType.INDEX_TTS
            language: 语言地区过滤，如 "zh-CN", "zh-TW"
            gender: 性别过滤，"Male" 或 "Female"
            age_group: 年龄组过滤，如 "青年", "中年", "少女"

        Returns:
            List[TTSVoice]: 过滤后的语音列表
        """
        try:
            # 构建查询条件，locale 过滤转换为前缀匹配
            locale_filter = None
            if language:
                locale_filter = language

            # 从数据库获取语音元数据
            voice_metas = self.voice_db.get_voices_by_filters(
                tts_engine=engine_type.value if engine_type else None,
                gender=gender,
                age_group=age_group,
                locale=locale_filter,
            )

            # 转换为 TTSVoice 对象
            voices = []
            for voice_meta in voice_metas:
                # 如果指定了语言过滤，进行前缀匹配检查
                if language and not voice_meta.locale.startswith(language):
                    continue

                tts_voice = self._convert_voice_metadata_to_tts_voice(voice_meta)
                voices.append(tts_voice)

            logger.debug(f"从数据库获取到 {len(voices)} 个语音配置")
            return voices

        except Exception as e:
            logger.error(f"获取语音列表失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取语音列表失败: {str(e)}")

    def get_voice_by_id_and_engine(
        self, id_name: str, engine_type: TTSEngineType
    ) -> Optional[TTSVoice]:
        """根据 ID 和引擎类型获取单个语音

        Args:
            id_name: 语音ID名称
            engine_type: TTS引擎类型

        Returns:
            Optional[TTSVoice]: 找到的语音对象，如果不存在返回 None
        """
        logger.info(f"请求获取语音：ID='{id_name}', 引擎='{engine_type.value}'")
        try:
            voice_meta = self.voice_db.get_voice_by_id_and_engine(
                id_name, engine_type.value
            )
            if voice_meta:
                tts_voice = self._convert_voice_metadata_to_tts_voice(voice_meta)
                logger.info(f"成功找到语音：{tts_voice.display_name}")
                return tts_voice
            else:
                logger.warning(
                    f"未找到匹配的语音：ID='{id_name}', 引擎='{engine_type.value}'"
                )
                return None
        except Exception as e:
            logger.error(
                f"获取语音失败：ID='{id_name}', 引擎='{engine_type.value}', 错误：{e}"
            )
            raise HTTPException(status_code=500, detail=f"获取语音失败: {str(e)}")

    def get_voices_by_engine(self, engine_type: TTSEngineType) -> List[TTSVoice]:
        """根据引擎类型获取所有语音

        Args:
            engine_type: TTS引擎类型

        Returns:
            List[TTSVoice]: 该引擎支持的所有语音列表
        """
        logger.info(f"请求获取引擎 '{engine_type.value}' 的所有语音列表")
        try:
            voice_metas = self.voice_db.get_voices_by_engine(engine_type.value)
            voices = [
                self._convert_voice_metadata_to_tts_voice(vm) for vm in voice_metas
            ]
            logger.info(f"引擎 '{engine_type.value}' 支持 {len(voices)} 个语音")
            return voices
        except Exception as e:
            logger.error(f"获取引擎 '{engine_type.value}' 语音列表失败: {e}")
            raise HTTPException(
                status_code=500, detail=f"获取引擎语音列表失败: {str(e)}"
            )

    def get_all_voices(self) -> List[TTSVoice]:
        """获取所有语音

        Returns:
            List[TTSVoice]: 所有语音列表
        """
        logger.info("请求获取所有语音列表")
        try:
            voice_metas = self.voice_db.get_all_voices()
            voices = [
                self._convert_voice_metadata_to_tts_voice(vm) for vm in voice_metas
            ]
            logger.info(f"数据库中共有 {len(voices)} 个语音配置")
            return voices
        except Exception as e:
            logger.error(f"获取所有语音失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取所有语音失败: {str(e)}")

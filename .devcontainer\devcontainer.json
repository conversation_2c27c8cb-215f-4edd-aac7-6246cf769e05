{
  "name": "ParrotNovels Development (GPU)",
  
  // 选择版本：基础版本使用 docker-compose.yml，GPU版本使用 docker-compose.gpu.yml
  // "dockerComposeFile": "docker-compose.yml",
  "dockerComposeFile": "docker-compose.gpu.yml",  // GPU 支持已启用
  
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  
  // 在容器创建后运行的命令 - GPU 环境初始化
  "postCreateCommand": "bash .devcontainer/setup.sh && chmod +x /workspace/.devcontainer/*.sh",
  
  // 端口转发
  "forwardPorts": [8000, 5173, 2024, 8888],
  
  // 基本挂载
  "mounts": [
    "source=${localWorkspaceFolder}/resources,target=/workspace/resources,type=bind"
  ],
  
  // 容器配置 (GPU 版本)
  "runArgs": ["--name=parrot-novels-dev-gpu", "--gpus=all"],
  "remoteUser": "root"
} 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI 依赖项
提供在API路由中使用的依赖注入功能
"""

from typing import Annotated

from fastapi import Depends, HTTPException

from ..tts.manager import TTSManager
from .system_init import get_tts_manager, is_system_initialized


def get_tts_manager_dependency() -> TTSManager:
    """获取TTS管理器依赖项

    Returns:
        TTSManager: TTS管理器实例

    Raises:
        HTTPException: 如果系统未初始化或TTS管理器不可用
    """
    if not is_system_initialized():
        raise HTTPException(status_code=503, detail="系统尚未初始化，请稍后重试")

    try:
        manager = get_tts_manager()
        if manager is None:
            raise HTTPException(status_code=503, detail="TTS管理器不可用")
        return manager
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"获取TTS管理器失败: {str(e)}")


# 类型注解，用于路由函数参数
TTSManagerDep = Annotated[TTSManager, Depends(get_tts_manager_dependency)]

# Node
**/node_modules/
**/dist/
**/build/
**/.cache/
**/pnpm-lock.yaml


# Editor and IDE Settings
**/*.code-workspace
**/*.swp
**/*.swo
**/.idea/

# OS-specific
**/Thumbs.db
**/ehthumbs.db
**/.Trashes
**/.DS_Store

# Logs
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/pnpm-debug.log*
**/lerna-debug.log*
**/*.log

# Environment Variables and Configs
.env
.env.*
!.env.example
!.env.test

# npm / Yarn / pnpm Lock Files (if using one exclusively)
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml


# git
.git
.github
.standard-firmware
**/temp

# Ignore OS generated files
.DS_Store
Thumbs.db

# 数据库文件
**/*.db
**/*.sqlite
!resources/data.db

# Python
**/.venv/
**/__pycache__
*.pyc
*.pyo
*.pyd
.Python
.ipynb_checkpoints/
**/*.ipynb

# .dev
.dev/
uv.lock

# Testing
# *.test.*
# *.spec.*

# Large media fles
*.mp4
*.mov
*.pdf
*.zip
*.mp3
*.wav
*.tar.gz

checkpoints/

**/.pytest_cache/
**/.coverage


# LangGraph
*.pckl

# YoYo AI version control directory
.yoyo/

.serena/
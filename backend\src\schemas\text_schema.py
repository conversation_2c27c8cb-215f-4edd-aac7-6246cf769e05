from typing import Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class TextFormatRequest(BaseModel):
    """文本格式化请求"""

    content: str = Field(
        ..., description="要格式化的小说内容", example="第一章 开始\n\n这是一个故事..."
    )
    enable_paragraph_indent: Optional[bool] = Field(
        False, description="是否启用段落缩进", example=False
    )
    indent_spaces: Optional[int] = Field(
        4, description="缩进空格数", ge=0, le=8, example=4
    )
    enable_chapter_formatting: Optional[bool] = Field(
        True, description="是否启用章节标题格式化", example=True
    )
    chapter_pattern: Optional[str] = Field(
        None,
        description="自定义章节标题正则表达式模式",
        example=r"^(第[\u4e00-\u9fa5\d零一二三四五六七八九十百千万亿]+章|Chapter\s+\d+.*)$",
    )
    remove_extra_spaces: Optional[bool] = Field(
        True, description="是否移除多余空格", example=True
    )
    normalize_line_breaks: Optional[bool] = Field(
        True, description="是否统一换行符", example=True
    )
    normalize_quotes: Optional[bool] = Field(
        True, description="是否统一引号格式为「」", example=True
    )



class TextFormatResponse(BaseModel):
    """文本格式化响应"""

    success: bool = Field(..., description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    formatted_content: Optional[str] = Field(None, description="格式化后的内容")
    original_length: Optional[int] = Field(None, description="原始内容长度")
    formatted_length: Optional[int] = Field(None, description="格式化后内容长度")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    chapter_count: Optional[int] = Field(None, description="检测到的章节数量")


class TextCleanRequest(BaseModel):
    """文本清理请求"""

    content: str = Field(..., description="要清理的文本内容")
    remove_bom: Optional[bool] = Field(True, description="是否移除BOM头")
    normalize_line_breaks: Optional[bool] = Field(True, description="是否统一换行符")
    remove_extra_whitespace: Optional[bool] = Field(
        True, description="是否移除多余空白字符"
    )
    remove_empty_lines: Optional[bool] = Field(True, description="是否移除多余空行")


class TextCleanResponse(BaseModel):
    """文本清理响应"""

    success: bool = Field(..., description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    cleaned_content: Optional[str] = Field(None, description="清理后的内容")
    original_length: Optional[int] = Field(None, description="原始内容长度")
    cleaned_length: Optional[int] = Field(None, description="清理后内容长度")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")


# 新增角色提取相关模型
class ExtractCharactersRequest(BaseModel):
    """角色提取请求"""

    content: str = Field(..., description="小说内容")
    use_ai: Optional[bool] = Field(True, description="是否使用AI分析")
    novel_type: Optional[Literal["modern", "ancient", "auto"]] = Field(
        "auto", description="小说类型"
    )
    max_characters: Optional[int] = Field(10, description="最大角色数量", ge=1, le=20)


class Character(BaseModel):
    """角色信息"""

    name: str = Field(..., description="角色名称")
    gender: Optional[str] = Field(None, description="性别")
    age_group: Optional[str] = Field(None, description="年龄段")
    role_type: Optional[str] = Field(None, description="角色类型：主角/配角/旁白")
    description: Optional[str] = Field(None, description="角色描述")
    dialogue_count: Optional[int] = Field(None, description="对话次数")


class ExtractCharactersResponse(BaseModel):
    """角色提取响应"""

    success: bool = Field(..., description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    characters: Optional[List[Character]] = Field(None, description="角色列表")
    dialogue_count: Optional[int] = Field(None, description="总对话数量")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")


# 新增文本分割相关模型
class SplitTextRequest(BaseModel):
    """文本分割请求"""

    content: str = Field(..., description="要分割的文本")
    split_type: Literal["chapter", "dialogue", "paragraph"] = Field(
        "chapter", description="分割类型"
    )
    max_length: Optional[int] = Field(500, description="最大长度")
    min_length: Optional[int] = Field(50, description="最小长度")


class TextSegment(BaseModel):
    """文本片段"""

    sequence: int = Field(..., description="序号")
    content: str = Field(..., description="内容")
    length: int = Field(..., description="长度")
    segment_type: str = Field(..., description="类型")


class SplitTextResponse(BaseModel):
    """文本分割响应"""

    success: bool = Field(..., description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    segments: Optional[List[TextSegment]] = Field(None, description="分割片段")
    total_segments: Optional[int] = Field(None, description="总片段数")
    original_length: Optional[int] = Field(None, description="原始长度")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")


class ProcessNovelRequest(BaseModel):
    """处理小说的请求体"""

    content: str = Field(..., description="小说原始内容")
    title: Optional[str] = Field(None, description="小说标题（可选）")

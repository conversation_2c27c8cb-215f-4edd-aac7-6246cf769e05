version: '3.8'

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # 构建参数
        PYTHON_VERSION: "3.12"
        NODE_VERSION: "22"
        CONDA_VERSION: "latest"
    
    # 容器名称
    container_name: parrot-novels-dev
    
    # 工作目录
    working_dir: /workspace
    
    # 挂载配置
    volumes:
      # 源代码挂载（绑定挂载，用于实时开发）
      - ../:/workspace:cached
      
      # 前端 node_modules 专用挂载（避免跨平台兼容性问题）
      - frontend-node-modules:/workspace/frontend/node_modules
      
      # 缓存挂载（命名卷，提高性能）
      - conda-cache:/opt/conda/pkgs
      - pip-cache:/root/.cache/pip
      - npm-cache:/root/.npm
      - yarn-cache:/root/.cache/yarn
      
      # 模型和资源文件（绑定挂载，但使用缓存一致性）
      - ../resources:/workspace/resources:cached
      
      # Git 配置挂载（可选，保持 Git 配置）
      - ~/.gitconfig:/root/.gitconfig:ro
      - ~/.ssh:/root/.ssh:ro
      
      # VS Code 服务器数据持久化
      - vscode-server:/root/.vscode-server
      - vscode-server-insiders:/root/.vscode-server-insiders
    
    # 端口映射
    ports:
      - "8000:8000"   # FastAPI 后端
      - "5173:5173"   # Vite 前端开发服务器
      - "2024:2024"   # LangGraph 开发服务器
      - "6080:6080"   # 桌面环境 (NoVNC)
      - "5901:5901"   # VNC 端口
    
    # 环境变量文件
    env_file:
      - devcontainer.env
    
    # 额外环境变量（覆盖env_file中的配置）
    environment:
      # 显示相关（用于可能的 GUI 应用）
      - DISPLAY=:1
      - VNC_RESOLUTION=1920x1080x24
    
    # 设备挂载（用于 GPU 支持，可选）
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]
    
    # 网络配置
    networks:
      - parrot-network
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 重启策略
    restart: unless-stopped
    
    # 保持容器运行
    tty: true
    stdin_open: true
    
    # 安全配置
    cap_add:
      - SYS_PTRACE  # 用于调试
    
    # 共享内存大小（用于音频处理）
    shm_size: 1g

# 网络配置
networks:
  parrot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  # 缓存卷
  conda-cache:
    driver: local
  pip-cache:
    driver: local
  npm-cache:
    driver: local
  yarn-cache:
    driver: local
  
  # 前端 node_modules 专用卷
  frontend-node-modules:
    driver: local
  
  # VS Code 服务器数据
  vscode-server:
    driver: local
  vscode-server-insiders:
    driver: local 
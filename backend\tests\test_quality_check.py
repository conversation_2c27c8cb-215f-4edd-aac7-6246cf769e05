"""测试质量检查模块的角色对比功能"""

import pytest

from backend.src.llm.workflows.quality_controller import QualityController


class MockWorkflow:
    """模拟工作流实例"""

    def handle_error(self, state, error):
        return {**state, "error": str(error), "status": "failed"}


@pytest.fixture
def quality_controller():
    """创建质量控制器实例"""
    mock_workflow = MockWorkflow()
    return QualityController(mock_workflow)


@pytest.fixture
def sample_state_with_quality_data():
    """创建包含质量检查数据的示例状态"""
    return {
        "workflow_id": "test_workflow_456",
        "novel_content": "这是一个测试小说。张三对李四说：你好世界！李四回答：是的，这是个好世界。",  # 34个中文字符
        "characters": [
            # 精炼后的角色（只有在对话中出现的）
            {
                "name": "张三",
                "type": "主角",
                "description": "主要角色",
                "personality": "正直",
                "speech_style": "直接",
            },
            {
                "name": "李四",
                "type": "配角",
                "description": "配角",
                "personality": "幽默",
                "speech_style": "风趣",
            },
        ],
        "original_characters": [
            # 原始分析出的角色（包含未在对话中出现的）
            {
                "name": "张三",
                "type": "主角",
                "description": "主要角色",
                "personality": "正直",
                "speech_style": "直接",
            },
            {
                "name": "李四",
                "type": "配角",
                "description": "配角",
                "personality": "幽默",
                "speech_style": "风趣",
            },
            {
                "name": "王五",
                "type": "反派",
                "description": "反派角色",
                "personality": "奸诈",
                "speech_style": "阴险",
            },
            {
                "name": "赵六",
                "type": "配角",
                "description": "次要角色",
                "personality": "谨慎",
                "speech_style": "保守",
            },
        ],
        "annotated_chapters": [
            {
                "title": "第一章",
                "annotation_status": "success",
                "dialogue_annotations": [
                    {
                        "type": "narration",
                        "speaker": "旁白",
                        "emotion": "neutral",
                        "content": "这是一个测试小说。张三对李四说：",  # 14个中文字符
                    },
                    {
                        "type": "dialogue",
                        "speaker": "张三",
                        "emotion": "happy",
                        "content": "你好世界！",  # 5个中文字符
                    },
                    {
                        "type": "narration",
                        "speaker": "旁白",
                        "emotion": "neutral",
                        "content": "李四回答：",  # 5个中文字符
                    },
                    {
                        "type": "dialogue",
                        "speaker": "李四",
                        "emotion": "neutral",
                        "content": "是的，这是个好世界。",  # 9个中文字符
                    },
                ],
            },
        ],
        "retry_count": 0,
    }


def test_character_retention_rate_calculation(
    quality_controller, sample_state_with_quality_data
):
    """测试角色保留率计算功能"""
    result = quality_controller.quality_check(sample_state_with_quality_data)

    # 检查基本结构
    assert result["step"] == "decide_after_quality_check"
    assert "quality_scores" in result

    quality_scores = result["quality_scores"]

    # 检查角色保留率是否正确计算
    assert "character_retention_rate" in quality_scores
    # 精炼后2个角色，原始4个角色，保留率应该是 2/4 = 0.5
    assert quality_scores["character_retention_rate"] == 0.5


def test_content_integrity_check(quality_controller, sample_state_with_quality_data):
    """测试文本内容完整性检查功能"""
    result = quality_controller.quality_check(sample_state_with_quality_data)
    quality_scores = result["quality_scores"]

    # 检查内容完整性指标是否存在
    assert "content_integrity" in quality_scores

    # 新的有意义字符统计法：
    # 原文包含标点：这是一个测试小说。张三对李四说：你好世界！李四回答：是的，这是个好世界。
    # 对话提取也包含标点，保留率应该很高（90%+），在完美范围内
    content_integrity_score = quality_scores["content_integrity"]
    assert content_integrity_score >= 0.8  # 应该至少是良好评分


def test_content_integrity_with_missing_content():
    """测试内容缺失时的完整性检查"""
    mock_workflow = MockWorkflow()
    quality_controller = QualityController(mock_workflow)

    state_with_missing_content = {
        "workflow_id": "test_missing_content",
        "novel_content": "这是一个很长的小说内容，包含很多字符和情节。张三激动地说道。",  # 有意义字符约25个
        "characters": [{"name": "张三", "type": "主角"}],
        "original_characters": [{"name": "张三", "type": "主角"}],
        "annotated_chapters": [
            {
                "dialogue_annotations": [
                    {
                        "type": "dialogue",
                        "speaker": "张三",
                        "content": "短对话",  # 只有3个有意义字符，保留率很低
                    }
                ]
            }
        ],
        "retry_count": 0,
    }

    result = quality_controller.quality_check(state_with_missing_content)
    quality_scores = result["quality_scores"]

    # 保留率约为 3/25 = 12%，远低于75%阈值，应该得低分
    content_integrity_score = quality_scores["content_integrity"]
    assert content_integrity_score == 0.3  # 超出合理范围的低分


def test_quality_check_decision_logic(quality_controller):
    """测试质量检查决策逻辑"""
    # 测试通过的情况
    good_state = {
        "workflow_id": "test_good",
        "novel_content": "测试内容",
        "characters": [{"name": "张三"}, {"name": "李四"}],
        "original_characters": [{"name": "张三"}, {"name": "李四"}],  # 100%保留率
        "annotated_chapters": [
            {
                "dialogue_annotations": [
                    {"content": "测试内容"}  # 完美匹配
                ]
            }
        ],
        "retry_count": 0,
        "quality_scores": {
            "character_retention_rate": 1.0,  # 完美保留
            "content_integrity": 1.0,  # 完美完整性
            "overall": 1.0,
        },
    }

    decision = quality_controller.decide_after_quality_check(good_state)
    assert decision == "finalize"

    # 测试失败但可重试的情况
    poor_state = {
        "workflow_id": "test_poor",
        "retry_count": 0,
        "quality_scores": {
            "character_retention_rate": 0.2,  # 低于30%阈值
            "content_integrity": 0.4,  # 低于60%阈值
            "overall": 0.3,
        },
    }

    decision = quality_controller.decide_after_quality_check(poor_state)
    assert decision == "retry"

    # 测试达到最大重试次数的情况
    failed_state = {
        "workflow_id": "test_failed",
        "retry_count": 2,  # 达到最大重试次数
        "quality_scores": {
            "character_retention_rate": 0.1,
            "content_integrity": 0.3,
            "overall": 0.2,
        },
    }

    decision = quality_controller.decide_after_quality_check(failed_state)
    assert decision == "failed"

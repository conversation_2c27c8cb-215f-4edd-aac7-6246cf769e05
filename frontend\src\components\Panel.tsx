import type { ReactNode } from 'react';

interface PanelProps {
  title: string;
  children: ReactNode;
  className?: string;
  variant?: 'modern' | 'square';
}

export default function Panel({
  title,
  children,
  className = '',
  variant = 'modern',
}: PanelProps) {
  const cardClass = variant === 'square' ? 'card-square' : 'card-modern';
  const headerClass =
    variant === 'square' ? 'panel-header-square' : 'panel-header-modern';

  return (
    <div className={`${cardClass} fade-in ${className}`}>
      <div className={headerClass}>
        <span>{title}</span>
      </div>
      <div className="p-5">{children}</div>
    </div>
  );
}

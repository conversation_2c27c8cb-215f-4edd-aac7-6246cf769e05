from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, Iterator, Optional

# 导入相关模块
try:
    from ..utils.logging_config import get_logger
    from ..utils.path_utils import ensure_dir_exists, resolve_resource_path
except ImportError:
    from utils.logging_config import get_logger
    from utils.path_utils import ensure_dir_exists, resolve_resource_path

logger = get_logger(__name__)


class TTSEngineType(str, Enum):
    """TTS引擎类型枚举"""

    EDGE_TTS = "edge"
    INDEX_TTS = "index"
    MINIMAX_TTS = "minimax"


class TTSEngineBase(ABC):
    """TTS基类，定义统一接口"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        # 初始化日志记录器
        import logging

        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def synthesize(
        self,
        text: str,
        voice_id_name: str,
        voice_style_name: str,
        output_path: Optional[str] = None,
        **kwargs,
    ) -> bytes:
        """合成语音

        Args:
            text: 要合成的文本
            voice_id_name: 语音ID或名称（必须）
            voice_style_name: 语音风格名称（必须）
            output_path: 输出文件路径（可选）
            **kwargs: 其他参数

        Returns:
            音频数据（bytes）
        """
        pass

    @abstractmethod
    def synthesize_stream(
        self, text: str, voice_id_name: str, voice_style_name: str, **kwargs
    ) -> Iterator[bytes]:
        """流式合成语音

        Args:
            text: 要合成的文本
            voice_id_name: 语音ID或名称（必须）
            voice_style_name: 语音风格名称（必须）
            **kwargs: 其他参数

        Yields:
            音频数据块（bytes）
        """
        pass

    @abstractmethod
    def get_configurable_parameters(self) -> Dict[str, Dict[str, Any]]:
        """获取TTS引擎可配置的参数信息

        Returns:
            参数配置字典，键为参数名，值为参数信息字典，包含:
            - type: 参数类型，如 'int', 'float', 'str', 'bool', 'select'
            - control: UI控件类型，如 'slider', 'switch', 'select', 'input'
            - label: 用户友好的显示标签
            - range: 参数范围，对于数值类型为 [min, max]，对于字符串类型为可选值列表
            - default: 默认值
            - description: 参数描述
            - unit: 单位（可选），如 '%', 'Hz', 'x'
            - step: 步长（数值类型可选）
            - category: 参数分类（可选），用于UI分组
            - depends_on: 依赖的参数（可选），如 {'param_name': 'value'}
            - advanced: 是否为高级参数（可选），默认为 False

        Example:
            {
                'rate': {
                    'type': 'int',
                    'control': 'slider',
                    'label': '语速',
                    'range': [-100, 100],
                    'default': 0,
                    'description': '语速调节，负值减慢，正值加快',
                    'unit': '%',
                    'step': 1,
                    'category': 'basic'
                },
                'enable_fast_mode': {
                    'type': 'bool',
                    'control': 'switch',
                    'label': '快速模式',
                    'default': False,
                    'description': '启用快速推理模式',
                    'category': 'advanced'
                },
                'voice_type': {
                    'type': 'select',
                    'control': 'select',
                    'label': '声音类型',
                    'range': ['male', 'female', 'child'],
                    'default': 'female',
                    'description': '选择声音类型',
                    'category': 'basic'
                }
            }
        """
        pass

    @abstractmethod
    def initialize(self) -> bool:
        """初始化 TTS 引擎

        Returns:
            bool: True 表示初始化成功，False 表示初始化失败
        """
        pass

    @abstractmethod
    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息

        Returns:
            引擎信息字典，包含:
            - name: 引擎名称
            - version: 引擎版本
            - description: 引擎描述
            - type: 引擎类型（可选）
            - models: 支持的模型列表
            - supported_languages: 支持的语言列表
            - supports_streaming: 是否支持流式合成
            - [其他特定于引擎的信息]

        Example:
            {
                "name": "EdgeTTS",
                "version": "1.0.0",
                "description": "Microsoft Edge浏览器内置的语音合成引擎",
                "type": "cloud",
                "models": ["default"],
                "supported_languages": ["zh-CN", "en-US"],
                "supports_streaming": True,
            }
        """
        pass

    def save_audio(self, audio_data: bytes, output_path: str) -> None:
        """保存音频数据到文件"""

        # 构建保存路径：resources/TTS/out/当前日期/output_path
        full_output_path = resolve_resource_path(f"TTS/{output_path}")

        # 确保目录存在
        ensure_dir_exists(full_output_path.parent)

        # 保存音频文件
        with open(full_output_path, "wb") as f:
            f.write(audio_data)

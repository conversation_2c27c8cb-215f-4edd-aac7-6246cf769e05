import type { APIResponse } from '../types';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// API 基础配置
const API_BASE_URL = 'http://localhost:8000';

// 通用 API 请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<APIResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.detail || '请求失败',
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误',
    };
  }
}

// 章节信息接口
export interface ChapterInfo {
  chapter_num: number;
  title: string;
  content: string;
  word_count: number;
  dialogue_count: number;
}

// 对话片段接口
export interface DialogueSegment {
  sequence: number;
  content: string;
  segment_type: 'dialogue' | 'narration' | 'action';
  character_hint?: string;
}

// 格式化请求接口 - 与后端保持一致
export interface FormatTextRequest {
  content: string;
  max_line_width?: number;
  enable_paragraph_indent?: boolean;
  indent_spaces?: number;
  enable_chapter_formatting?: boolean;
  chapter_pattern?: string;
  remove_extra_spaces?: boolean;
  normalize_line_breaks?: boolean;
  // 新增小说特定参数
  novel_type?: 'modern' | 'ancient' | 'auto';
  enable_dialogue_formatting?: boolean;
  enable_chapter_detection?: boolean;
  normalize_quotes?: boolean;
  enable_character_preprocessing?: boolean;
  preserve_chapter_numbers?: boolean;
}

// 格式化响应接口 - 与后端保持一致
export interface FormatTextResponse {
  success: boolean;
  message?: string;
  formatted_content?: string;
  original_length?: number;
  formatted_length?: number;
  processing_time?: number;
  // 新增小说特定信息
  detected_novel_type?: string;
  chapter_count?: number;
  chapters?: ChapterInfo[];
  dialogue_segments?: DialogueSegment[];
  character_hints?: string[];
  format_statistics?: {
    dialogue_count: number;
    narration_count: number;
    character_hints_count: number;
    compression_ratio: number;
  };
}

// 兼容旧版本的格式化选项（为了平滑迁移）
export interface LegacyFormatOptions {
  remove_extra_lines?: boolean;
  normalize_quotes?: boolean;
  semantic_segmentation?: boolean;
  format_dialogue?: boolean;
  optimize_paragraphs?: boolean;
  add_chapter_marks?: boolean;
}

// 旧版本格式化请求接口（向后兼容）
export interface LegacyFormatTextRequest {
  content: string;
  options?: LegacyFormatOptions;
}

// 文本片段接口
export interface TextSegment {
  sequence: number;
  content: string;
  length: number;
  segment_type: string;
}

// 切割请求接口
export interface SplitTextRequest {
  content: string;
  split_type?: 'chapter' | 'dialogue' | 'paragraph';
  max_length?: number;
  min_length?: number;
}

// 切割响应接口
export interface SplitTextResponse {
  success: boolean;
  message?: string;
  segments?: TextSegment[];
  total_segments?: number;
  original_length?: number;
  processing_time?: number;
}

// 角色信息接口
export interface Character {
  name: string;
  gender?: string;
  age_group?: string;
  role_type?: string;
  description?: string;
  dialogue_count?: number;
  // TTS相关属性
  voice_id?: string;
  voice_style?: string;
}

// 角色提取请求接口
export interface ExtractCharactersRequest {
  content: string;
  use_ai?: boolean;
  novel_type?: 'modern' | 'ancient' | 'auto';
  max_characters?: number;
}

// 角色提取响应接口
export interface ExtractCharactersResponse {
  success: boolean;
  message?: string;
  characters?: Character[];
  dialogue_count?: number;
  processing_time?: number;
}

// >> 新增：流式处理接口的类型定义
interface ProcessNovelStreamPayload {
  content: string;
  title?: string;
}

// SSE 'update' 事件的数据结构
interface SSEUpdateData {
  node: string;
  progress: number;
  status: string;
  current_chapter: number;
  total_chapters: number;
  characters: Character[];
}

// 对话项的结构
interface DialogueItem {
  type: 'dialogue' | 'narration';
  speaker: string;
  emotion: string;
  content: string;
}

// SSE 'final' 事件的数据结构
interface SSEFinalData {
  // 新的格式：包含章节数据和角色列表
  chapters: { [chapterKey: string]: DialogueItem[] }; // chapterKey 形如 "第0章"、"第1章" 等
  characters: Character[]; // 角色列表
  message?: string; // 可选的消息
}

// processNovelStream 函数的参数类型
interface ProcessNovelStreamOptions {
  content: string;
  title?: string;
  onUpdate: (data: SSEUpdateData) => void;
  onFinal: (data: SSEFinalData) => void;
  onError: (error: Error) => void;
  onClose: () => void;
}
// << 新增结束

// 工具函数：将旧版本格式化选项转换为新版本参数
export function convertLegacyFormatOptions(
  content: string,
  legacyOptions?: LegacyFormatOptions
): FormatTextRequest {
  const options = legacyOptions || {};

  return {
    content,
    max_line_width: 80,
    enable_paragraph_indent: true,
    indent_spaces: 4,
    enable_chapter_formatting: options.add_chapter_marks !== false,
    remove_extra_spaces: options.remove_extra_lines !== false,
    normalize_line_breaks: true,
    // 新参数映射
    novel_type: 'auto',
    enable_dialogue_formatting: options.format_dialogue !== false,
    enable_chapter_detection: true,
    normalize_quotes: options.normalize_quotes !== false,
    enable_character_preprocessing: false, // 默认关闭，避免性能影响
    preserve_chapter_numbers: true,
  };
}

// 文本API服务
export const textApi = {
  // 格式化文本 - 新版本接口
  formatText: (request: FormatTextRequest) =>
    apiRequest<FormatTextResponse>('/api/text/format', {
      method: 'POST',
      body: JSON.stringify(request),
    }),

  // 格式化文本 - 向后兼容接口
  formatTextLegacy: (request: LegacyFormatTextRequest) => {
    const newRequest = convertLegacyFormatOptions(
      request.content,
      request.options
    );
    return textApi.formatText(newRequest);
  },

  // 切割文本
  splitText: (request: SplitTextRequest) =>
    apiRequest<SplitTextResponse>('/api/text/split', {
      method: 'POST',
      body: JSON.stringify(request),
    }),

  // 提取角色
  extractCharacters: (request: ExtractCharactersRequest) =>
    apiRequest<ExtractCharactersResponse>('/api/text/extract-characters', {
      method: 'POST',
      body: JSON.stringify(request),
    }),

  // 快速格式化（推荐设置）
  quickFormat: (
    content: string,
    novelType: 'modern' | 'ancient' | 'auto' = 'auto'
  ) =>
    textApi.formatText({
      content,
      max_line_width: 80,
      enable_paragraph_indent: false,
      indent_spaces: 4,
      enable_chapter_formatting: true,
      remove_extra_spaces: true,
      normalize_line_breaks: true,
      novel_type: novelType,
      enable_dialogue_formatting: true,
      enable_chapter_detection: true,
      normalize_quotes: true,
      enable_character_preprocessing: false,
      preserve_chapter_numbers: true,
    }),

  // 深度格式化（包含角色识别）
  deepFormat: (
    content: string,
    novelType: 'modern' | 'ancient' | 'auto' = 'auto'
  ) =>
    textApi.formatText({
      content,
      max_line_width: 80,
      enable_paragraph_indent: false,
      indent_spaces: 4,
      enable_chapter_formatting: true,
      remove_extra_spaces: true,
      normalize_line_breaks: true,
      novel_type: novelType,
      enable_dialogue_formatting: true,
      enable_chapter_detection: true,
      normalize_quotes: true,
      enable_character_preprocessing: true,
      preserve_chapter_numbers: true,
    }),

  // 获取格式化帮助信息
  getFormatHelp: () => apiRequest<any>('/api/text/format/help'),

  // >> 新增：流式处理小说接口
  processNovelStream: async (options: ProcessNovelStreamOptions) => {
    const { content, title, onUpdate, onFinal, onError, onClose } = options;

    const payload: ProcessNovelStreamPayload = { content, title };

    await fetchEventSource(`${API_BASE_URL}/api/text/process-novel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream',
      },
      body: JSON.stringify(payload),

      onopen: async (response) => {
        if (!response.ok) {
          const errorJson = await response
            .json()
            .catch(() => ({ detail: '无法连接到流式服务' }));
          throw new Error(errorJson.detail || `HTTP Error ${response.status}`);
        }
      },

      onmessage(ev) {
        try {
          // 检查数据是否为空或无效
          if (!ev.data || ev.data.trim() === '') {
            console.warn('收到空的 SSE 数据，跳过处理');
            return;
          }

          // 解析后端发送的数据
          const parsedData = JSON.parse(ev.data);

          if (ev.event === 'message') {
            // 后端发送的 message 事件包含 type 字段
            if (parsedData.type === 'status') {
              // 构造符合前端期望的更新数据格式
              const updateData: SSEUpdateData = {
                node: parsedData.content || '处理中',
                progress: parsedData.progress || 0,
                status: parsedData.content || '处理中',
                current_chapter: 0, // 后端暂未提供此字段
                total_chapters: 0, // 后端暂未提供此字段
                characters: [], // 后端暂未在 status 中提供此字段
              };
              onUpdate(updateData);
            }
          } else if (ev.event === 'final') {
            // 后端发送的 final 事件
            if (parsedData.type === 'result') {
              // 直接传递后端返回的结构化数据，不添加 characters 字段
              const finalData = parsedData.content; // 只包含 "第0章"、"第1章" 等章节数据
              onFinal(finalData);
            }
          } else if (ev.event === 'error') {
            const errorData = parsedData;
            throw new Error(errorData.message || '流处理过程中发生未知错误');
          }
        } catch (e) {
          // 如果是 JSON 解析错误，输出更详细的信息
          if (e instanceof SyntaxError) {
            console.warn('JSON 解析失败，原始数据:', ev.data);
            console.warn('这通常在 SSE 连接结束时发生，可以安全忽略');
            return; // 不抛出错误，避免干扰正常流程
          }

          if (e instanceof Error) {
            onError(e);
          } else {
            onError(new Error('解析SSE消息时发生错误'));
          }
        }
      },

      onerror(err) {
        onError(err instanceof Error ? err : new Error('未知流错误'));
        // 必须抛出错误以停止重试
        throw err;
      },

      onclose() {
        onClose();
      },
    });
  },
  // << 新增结束

  // 健康检查
  health: () =>
    apiRequest<{
      status: string;
      service: string;
      version: string;
      features: string[];
    }>('/api/text/health'),
};

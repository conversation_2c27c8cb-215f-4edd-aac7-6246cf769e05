// TTS相关类型定义

// TTS模式类型
export type TTSMode = 'single' | 'multi-character';

// 引擎声音样式
export interface VoiceStyle {
  [styleName: string]: string; // 样式名 -> 音频文件路径
}

// 引擎声音配置
export interface EngineVoice {
  id_name: string;
  gender: 'Male' | 'Female';
  display_name: string;
  locale: string;
  age_group: string;
  engines: string[];
  description: string;
  styles: VoiceStyle;
}

// 引擎参数配置
export interface EngineParameter {
  type: 'int' | 'float' | 'str' | 'bool' | 'select';
  control: 'slider' | 'switch' | 'select' | 'input';
  label: string;
  range?: number[] | string[];
  default: any;
  description: string;
  unit?: string;
  step?: number;
  category?: string;
  depends_on?: Record<string, any>;
  advanced?: boolean;
}

// TTS引擎配置
export interface TTSEngine {
  id: string;
  name: string;
  version: string;
  description: string;
  type: string;
  models: string[];
  is_available: boolean;
  supported_languages: string[];
  supports_streaming: boolean;
  parameters: Record<string, EngineParameter>;
  voices: EngineVoice[];
}

// 引擎响应类型
export interface TTSEnginesResponse {
  engines?: TTSEngine[];
  [index: number]: TTSEngine; // 支持数组索引
}

export interface TTSModel {
  id: string;
  name: string;
  description?: string;
}

// 为了向后兼容，保留原有VoiceOption接口
export interface VoiceOption {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  language?: string;
  styles?: string[];
  stylesPaths?: VoiceStyle; // 样式到文件路径的映射
}

export interface Character {
  id: string;
  name: string;
  emoji: string;
  percentage: number;
  voiceId: string;
  voiceStyle?: string; // 新增声音样式选择
}

export interface TTSSettings {
  engine: string;
  model: string;
  mode: TTSMode;
  singleVoiceId?: string;
  singleVoiceStyle?: string; // 新增单声音模式的样式选择
  parameters?: Record<string, any>; // 新增引擎参数
}

export interface NovelMeta {
  title: string;
  category: string;
  content: string;
}

export interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  currentCharacter: string;
  currentText: string;
  playbackSpeed: number;
  volume: number; // 音量值，范围 0-1
}

export interface TTSProject {
  id: string;
  novel: NovelMeta;
  characters: Character[];
  settings: TTSSettings;
  createdAt: Date;
  updatedAt: Date;
}

// TTS配置参数类型定义（向后兼容）
export interface TTSParameter {
  /** 参数类型 */
  type: 'int' | 'float' | 'str' | 'bool' | 'select';
  /** UI控件类型 */
  control: 'slider' | 'switch' | 'select' | 'input';
  /** 用户友好的显示标签 */
  label: string;
  /** 参数范围：数值类型为[min, max]，选择类型为选项列表 */
  range?: number[] | string[];
  /** 默认值 */
  default: any;
  /** 参数描述 */
  description: string;
  /** 单位（可选） */
  unit?: string;
  /** 步长（数值类型可选） */
  step?: number;
  /** 参数分类，用于UI分组 */
  category?: string;
  /** 依赖的参数 */
  depends_on?: Record<string, any>;
  /** 是否为高级参数 */
  advanced?: boolean;
}

export interface TTSParametersConfig {
  [paramName: string]: TTSParameter;
}

// 参数分类定义
export const PARAMETER_CATEGORIES = {
  basic: '基本参数',
  performance: '性能设置',
  advanced: '高级选项',
  voice: '声音设置',
} as const;

export type ParameterCategory = keyof typeof PARAMETER_CATEGORIES;

// UI控件类型定义
export const CONTROL_TYPES = {
  slider: 'slider',
  switch: 'switch',
  select: 'select',
  input: 'input',
} as const;

export type ControlType = keyof typeof CONTROL_TYPES;

// 参数值类型
export interface TTSParameterValues {
  [paramName: string]: any;
}

import json

import requests

group_id = ' '  # Type your group id
api_key = ' '  # Type your api key

#Audio file upload
url = f'https://api.minimaxi.chat/v1/files/upload?GroupId={group_id}'
headers1 = {
    'authority': 'api.minimaxi.chat',
    'Authorization': f'Bearer {api_key}'
}

data = {
    'purpose': 'voice_clone'
}

files = {
    'file': open('audio.mp3', 'rb')
}
response = requests.post(url, headers=headers1, data=data, files=files)
file_id = response.json().get("file").get("file_id")
print(file_id)

#Voice cloning
url = f'https://api.minimaxi.chat/v1/voice_clone?GroupId={group_id}'
payload2 = json.dumps({
  "file_id": file_id,
  "voice_id": "test1234"
})
headers2 = {
  'authorization': f'Bearer {api_key}',
  'content-type': 'application/json'
}
response = requests.request("POST", url, headers=headers2, data=payload2)
print(response.text)
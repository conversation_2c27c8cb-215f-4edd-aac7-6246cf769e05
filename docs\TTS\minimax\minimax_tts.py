import json
import requests
from typing import Optional, Dict, Any, Iterator, List
from tts_base import TTSBase


class MinimaxTTS(TTSBase):
    """Minimax TTS实现"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.group_id = self.config.get("group_id", "")
        self.api_key = self.config.get("api_key", "")
        self.model = self.config.get("model", "speech-02-turbo")
        self.voice_id = self.config.get("voice_id", "male-qn-qingse")
        self.url = f"https://api.minimaxi.chat/v1/t2a_v2?GroupId={self.group_id}"

    def _build_headers(self) -> Dict[str, str]:
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

    def _build_body(self, text: str, stream: bool = False, **kwargs) -> str:
        voice_setting = {
            "voice_id": kwargs.get("voice_id", self.voice_id),
            "speed": kwargs.get("speed", 1.0),
            "vol": kwargs.get("volume", 1.0),
            "pitch": kwargs.get("pitch", 0),
        }

        audio_setting = {
            "sample_rate": kwargs.get("sample_rate", 32000),
            "bitrate": kwargs.get("bitrate", 128000),
            "format": kwargs.get("format", "mp3"),
            "channel": kwargs.get("channel", 1),
        }

        body = {
            "model": kwargs.get("model", self.model),
            "text": text,
            "stream": stream,
            "voice_setting": voice_setting,
            "audio_setting": audio_setting,
        }

        return json.dumps(body)

    def synthesize(
        self, text: str, output_path: Optional[str] = None, **kwargs
    ) -> bytes:
        """合成语音"""
        headers = self._build_headers()
        body = self._build_body(text, stream=False, **kwargs)

        response = requests.post(self.url, headers=headers, data=body)
        response.raise_for_status()

        result = response.json()
        if "data" in result and "audio" in result["data"]:
            audio_data = bytes.fromhex(result["data"]["audio"])

            if output_path:
                self.save_audio(audio_data, output_path)

            return audio_data

        raise Exception(f"TTS failed: {result}")

    def synthesize_stream(self, text: str, **kwargs) -> Iterator[bytes]:
        """流式合成语音"""
        headers = self._build_headers()
        body = self._build_body(text, stream=True, **kwargs)

        response = requests.post(self.url, headers=headers, data=body, stream=True)
        response.raise_for_status()

        for chunk in response.raw:
            if chunk and chunk[:5] == b"data:":
                try:
                    data = json.loads(chunk[5:])
                    if "data" in data and "audio" in data["data"]:
                        audio_chunk = bytes.fromhex(data["data"]["audio"])
                        yield audio_chunk
                except json.JSONDecodeError:
                    continue

    def get_voices(
        self,
        languages: Optional[List[str]] = None,
        regions: Optional[List[str]] = None,
        gender: Optional[str] = None,
    ) -> List[Dict[str, str]]:
        """获取可用的语音列表

        Args:
            languages: 语言代码列表，如 ['zh', 'en']，可选
            regions: 方言/地区代码列表，如 ['CN', 'US']，可选
            gender: 性别过滤，'Male' 或 'Female'，可选

        Returns:
            语音信息列表，每个元素包含语音详细信息
        """
        # Minimax的语音列表，统一为中文语音
        all_voices = [
            {
                "name": "male-qn-qingse",
                "language": "zh",
                "region": "CN",
                "voice_name": "qingse",
                "display_name": "青涩青年男声",
                "gender": "Male",
                "locale": "zh-CN",
            },
            {
                "name": "female-shaonv",
                "language": "zh",
                "region": "CN",
                "voice_name": "shaonv",
                "display_name": "少女音",
                "gender": "Female",
                "locale": "zh-CN",
            },
            {
                "name": "male-qn-jingying",
                "language": "zh",
                "region": "CN",
                "voice_name": "jingying",
                "display_name": "精英男声",
                "gender": "Male",
                "locale": "zh-CN",
            },
            {
                "name": "female-qn-huoli",
                "language": "zh",
                "region": "CN",
                "voice_name": "huoli",
                "display_name": "活力女声",
                "gender": "Female",
                "locale": "zh-CN",
            },
        ]

        # 应用过滤条件
        result = []
        for voice in all_voices:
            if languages and voice["language"] not in languages:
                continue
            if regions and voice["region"] not in regions:
                continue
            if gender and voice["gender"] != gender:
                continue
            result.append(voice)

        return result

    def get_models(self) -> List[str]:
        """获取可用的模型列表

        Minimax支持四种模型
        """
        return ["speech-02-hd", "speech-02-turbo", "speech-01-hd", "speech-01-turbo"]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS 使用示例
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置 logging 日志
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper()),
    format=os.getenv("LOG_FORMAT", "%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s"),
    datefmt="%Y-%m-%d %H:%M:%S"
)

# 获取 logger
logger = logging.getLogger(__name__)

# 添加文件日志（如果配置了日志文件）
log_file = os.getenv("LOG_FILE")
if log_file:
    # 确保日志目录存在
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper()))
    
    # 设置文件日志格式
    file_formatter = logging.Formatter(
        os.getenv("LOG_FORMAT", "%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s"),
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(file_formatter)
    
    # 添加到根 logger
    logging.getLogger().addHandler(file_handler)

# 添加路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent.parent))

from index_tts import IndexTTS
from config_example import get_config


def basic_usage_example():
    """基本使用示例"""
    logger.info("=== IndexTTS 基本使用示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 要合成的文本
        text = "Hello, this is IndexTTS. 这是一个零样本语音合成系统。"
        
        # 合成语音
        logger.info(f"正在合成文本: {text}")
        audio_data = tts.synthesize(
            text=text,
            reference_voice=reference_voice,
            output_path="output_basic.wav"
        )
        
        logger.info(f"合成完成，音频数据大小: {len(audio_data)} bytes")
        logger.info("音频已保存到: output_basic.wav")
        
    except Exception as e:
        logger.error(f"基本使用示例失败: {e}")


def streaming_example():
    """流式合成示例"""
    logger.info("=== IndexTTS 流式合成示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 要合成的文本
        text = "这是流式合成的示例。This is a streaming synthesis example."
        
        # 流式合成
        logger.info(f"正在流式合成文本: {text}")
        audio_chunks = []
        
        for i, chunk in enumerate(tts.synthesize_stream(
            text=text,
            reference_voice=reference_voice
        )):
            audio_chunks.append(chunk)
            logger.debug(f"接收到音频块 {i+1}: {len(chunk)} bytes")
        
        # 合并所有音频块
        full_audio = b''.join(audio_chunks)
        
        # 保存完整音频
        tts.save_audio(full_audio, "output_streaming.wav")
        logger.info(f"流式合成完成，总音频大小: {len(full_audio)} bytes")
        logger.info("音频已保存到: output_streaming.wav")
        
    except Exception as e:
        logger.error(f"流式合成示例失败: {e}")


def mixed_language_example():
    """中英文混合示例"""
    logger.info("=== IndexTTS 中英文混合示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 中英文混合文本
        mixed_texts = [
            "Welcome to IndexTTS, 这是一个支持中英文混合的TTS系统。",
            "今天的天气很好，the weather is very nice today.",
            "我喜欢编程，I love programming, 特别是Python。",
            "Let's test some technical terms: 机器学习 machine learning, 深度学习 deep learning."
        ]
        
        for i, text in enumerate(mixed_texts):
            logger.info(f"正在合成第 {i+1} 段文本: {text}")
            
            output_path = f"output_mixed_{i+1}.wav"
            audio_data = tts.synthesize(
                text=text,
                reference_voice=reference_voice,
                output_path=output_path
            )
            
            logger.info(f"合成完成，音频大小: {len(audio_data)} bytes")
            logger.info(f"音频已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"中英文混合示例失败: {e}")


def pinyin_annotation_example():
    """拼音标注示例"""
    logger.info("=== IndexTTS 拼音标注示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 带拼音标注的文本（用于处理多音字和特殊发音）
        pinyin_texts = [
            "重(chóng)庆是一个美丽的城市",  # 多音字标注
            "银行(háng)和银行(xíng)是不同的概念",  # 同字不同音
            "这个乐(yuè)曲很快乐(lè)",  # 多音字区分
            "他要(yào)重(chóng)新考虑这个重(zhòng)要的问题",  # 复杂多音字
            "长(cháng)江大桥很长(cháng)",  # 重复多音字
        ]
        
        for i, text in enumerate(pinyin_texts):
            logger.info(f"正在合成第 {i+1} 段拼音标注文本: {text}")
            
            output_path = f"output_pinyin_{i+1}.wav"
            audio_data = tts.synthesize(
                text=text,
                reference_voice=reference_voice,
                output_path=output_path
            )
            
            logger.info(f"合成完成，音频大小: {len(audio_data)} bytes")
            logger.info(f"音频已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"拼音标注示例失败: {e}")


def voice_cloning_example():
    """语音克隆示例"""
    logger.info("=== IndexTTS 语音克隆示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 不同的参考语音文件
        reference_voices = [
            "path/to/male_voice.wav",    # 男声参考
            "path/to/female_voice.wav",  # 女声参考
            "path/to/child_voice.wav",   # 童声参考
        ]
        
        # 测试文本
        test_text = "这是语音克隆测试，IndexTTS 可以模仿不同的声音特征。"
        
        for i, ref_voice in enumerate(reference_voices):
            voice_type = ["男声", "女声", "童声"][i]
            logger.info(f"使用 {voice_type} 参考语音: {ref_voice}")
            logger.info(f"合成文本: {test_text}")
            
            output_path = f"output_clone_{voice_type}.wav"
            
            try:
                audio_data = tts.synthesize(
                    text=test_text,
                    reference_voice=ref_voice,
                    output_path=output_path
                )
                
                logger.info(f"克隆成功，音频大小: {len(audio_data)} bytes")
                logger.info(f"音频已保存到: {output_path}")
                
            except FileNotFoundError:
                logger.warning(f"参考语音文件不存在: {ref_voice}")
                logger.warning("请确保参考语音文件路径正确")
            except Exception as voice_error:
                logger.error(f"语音克隆失败: {voice_error}")
        
    except Exception as e:
        logger.error(f"语音克隆示例失败: {e}")


def batch_synthesis_example():
    """批量合成示例"""
    logger.info("=== IndexTTS 批量合成示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 批量文本列表
        text_list = [
            "第一段：欢迎使用 IndexTTS 语音合成系统。",
            "第二段：这是一个支持零样本语音克隆的 TTS 引擎。",
            "第三段：它可以处理中英文混合文本。",
            "第四段：The system supports both Chinese and English.",
            "第五段：批量合成可以提高处理效率。",
        ]
        
        logger.info(f"开始批量合成 {len(text_list)} 段文本...")
        
        # 创建输出目录
        output_dir = "batch_output"
        os.makedirs(output_dir, exist_ok=True)
        
        successful_count = 0
        failed_count = 0
        
        for i, text in enumerate(text_list, 1):
            try:
                logger.info(f"[{i}/{len(text_list)}] 正在合成: {text[:30]}...")
                
                output_path = os.path.join(output_dir, f"batch_{i:03d}.wav")
                audio_data = tts.synthesize(
                    text=text,
                    reference_voice=reference_voice,
                    output_path=output_path
                )
                
                logger.info(f"合成成功，音频大小: {len(audio_data)} bytes")
                logger.info(f"保存到: {output_path}")
                successful_count += 1
                
            except Exception as item_error:
                logger.error(f"合成失败: {item_error}")
                failed_count += 1
        
        logger.info(f"批量合成完成！")
        logger.info(f"成功: {successful_count} 个")
        logger.info(f"失败: {failed_count} 个")
        logger.info(f"输出目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"批量合成示例失败: {e}")


def performance_test_example():
    """性能测试示例"""
    logger.info("=== IndexTTS 性能测试示例 ===")
    
    try:
        import time
        
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 设置参考语音
        reference_voice = os.getenv("INDEX_TTS_REFERENCE_VOICE", "path/to/reference_voice.wav")
        
        # 不同长度的测试文本
        test_texts = {
            "短文本": "这是一个短文本测试。",
            "中等文本": "这是一个中等长度的文本测试，包含更多的内容来评估IndexTTS的性能表现。",
            "长文本": "这是一个较长的文本测试，用于评估IndexTTS在处理长文本时的性能表现。" * 5,
        }
        
        logger.info("开始性能测试...")
        
        for text_type, text in test_texts.items():
            logger.info(f"测试 {text_type} (长度: {len(text)} 字符)")
            logger.debug(f"文本内容: {text[:50]}...")
            
            # 记录开始时间
            start_time = time.time()
            
            try:
                audio_data = tts.synthesize(
                    text=text,
                    reference_voice=reference_voice,
                    output_path=f"performance_test_{text_type}.wav"
                )
                
                # 计算耗时
                end_time = time.time()
                duration = end_time - start_time
                
                # 计算性能指标
                chars_per_second = len(text) / duration
                audio_size_mb = len(audio_data) / (1024 * 1024)
                
                logger.info(f"✓ 合成成功")
                logger.info(f"  耗时: {duration:.2f} 秒")
                logger.info(f"  处理速度: {chars_per_second:.1f} 字符/秒")
                logger.info(f"  音频大小: {audio_size_mb:.2f} MB")
                logger.info(f"  音频时长估算: {len(audio_data) / 44100 / 2:.1f} 秒")  # 假设44.1kHz, 16bit
                
            except Exception as test_error:
                logger.error(f"✗ 测试失败: {test_error}")
        
        logger.info("性能测试完成！")
        
    except Exception as e:
        logger.error(f"性能测试示例失败: {e}")


def advanced_features_example():
    """高级功能示例"""
    logger.info("=== IndexTTS 高级功能示例 ===")
    
    try:
        # 初始化 IndexTTS
        config = get_config("default")
        tts = IndexTTS(config)
        
        # 1. 获取引擎信息
        logger.info("1. 引擎信息:")
        engine_info = tts.get_engine_info()
        for key, value in engine_info.items():
            logger.info(f"   {key}: {value}")
        
        # 2. 获取支持的语言
        logger.info("2. 支持的语言:")
        languages = tts.get_supported_languages()
        logger.info(f"   {', '.join(languages)}")
        
        # 3. 获取可用语音（IndexTTS 是零样本，返回说明）
        logger.info("3. 语音信息:")
        voices = tts.get_voices()
        for voice in voices:
            logger.info(f"   {voice['display_name']}")
            logger.info(f"   描述: {voice['description']}")
        
        # 4. 验证参考语音文件
        logger.info("4. 参考语音验证:")
        test_files = [
            "path/to/valid_voice.wav",
            "path/to/invalid_voice.txt",
            "nonexistent_file.wav"
        ]
        
        for file_path in test_files:
            is_valid = tts.validate_reference_voice(file_path)
            status = "✓ 有效" if is_valid else "✗ 无效"
            logger.info(f"   {file_path}: {status}")
        
        # 5. 设置默认参考语音
        logger.info("5. 设置默认参考语音:")
        try:
            tts.set_reference_voice("path/to/default_voice.wav")
            logger.info("   ✓ 默认参考语音设置成功")
        except FileNotFoundError:
            logger.warning("   ✗ 参考语音文件不存在")
        
    except Exception as e:
        logger.error(f"高级功能示例失败: {e}")


def main():
    """主函数，运行所有示例"""
    logger.info("IndexTTS 使用示例")
    logger.info("=" * 50)
    
    # 运行所有示例
    examples = [
        ("基础使用", basic_usage_example),
        ("流式合成", streaming_example),
        ("混合语言", mixed_language_example),
        ("拼音标注", pinyin_annotation_example),
        ("语音克隆", voice_cloning_example),
        ("批量合成", batch_synthesis_example),
        ("性能测试", performance_test_example),
        ("高级功能", advanced_features_example),
    ]
    
    for name, func in examples:
        try:
            logger.info(f"{'='*20} {name} {'='*20}")
            func()
        except Exception as e:
            logger.error(f"示例 '{name}' 运行失败: {e}")
    
    logger.info("=" * 50)
    logger.info("所有示例运行完成！")


if __name__ == "__main__":
    main()
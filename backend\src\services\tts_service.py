#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS服务
提供TTS引擎管理、参数获取和语音合成功能
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..schemas.tts_schema import (
    TTSEngineInfo,
    TTSParameter,
    TTSSynthesizeResponse,
)
from ..tts.engine import TTSEngineType
from ..tts.manager import TTSManager
from ..utils.logging_config import get_logger
from .voice_service import VoiceService

logger = get_logger(__name__)


class TTSService:
    """TTS服务类"""

    def __init__(self):
        # 使用单例的TTSManager实例
        self.tts_manager = TTSManager()
        # 初始化voice服务
        self.voice_service = VoiceService()

    async def get_engines_info(self) -> List[TTSEngineInfo]:
        """获取可用的TTS引擎列表、参数信息、支持的voice列表"""
        logger.info("开始获取可用的TTS引擎信息...")
        try:
            available_engines = self.tts_manager.list_available_engines()
            engine_infos = []

            for engine_id in available_engines:
                try:
                    logger.debug(f"尝试获取引擎 {engine_id} 信息")
                    # 直接获取引擎实例，由于启动时已完全初始化，这里会很快
                    engine = self.tts_manager.get_engine(engine_id)

                    if engine:
                        engine_info_dict = engine.get_engine_info()
                        # 将字符串engine_id转换为枚举类型
                        try:
                            engine_type = TTSEngineType(engine_id)
                        except ValueError:
                            # 如果不是已知的枚举值，跳过
                            logger.warning(f"未知的引擎类型: {engine_id}")
                            continue

                        engine_info = TTSEngineInfo(
                            id=engine_id,
                            name=engine_info_dict.get("name", engine_id),
                            version=engine_info_dict.get("version", "1.0.0"),
                            description=engine_info_dict.get("description", ""),
                            type=engine_info_dict.get("type", ""),
                            is_available=True,
                            models=engine_info_dict.get("models", []),
                            supported_languages=engine_info_dict.get(
                                "supported_languages", []
                            ),
                            supports_streaming=engine_info_dict.get(
                                "supports_streaming", False
                            ),
                            parameters=self.transfer_parameters(
                                engine.get_configurable_parameters()
                            ),
                            voices=self.voice_service.get_voices_by_engine(engine_type),
                        )
                        engine_infos.append(engine_info)
                        logger.debug(f"成功获取引擎 {engine_id} 信息")
                except Exception as e:
                    logger.warning(f"获取引擎 {engine_id} 信息失败: {e}")
                    # 添加基本信息，标记为不可用
                    engine_info = TTSEngineInfo(
                        id=engine_id,
                        name=engine_id,
                        description=f"引擎信息获取失败: {str(e)}",
                        version="unknown",
                        type="unknown",
                        is_available=False,
                        models=[],
                        supported_languages=[],
                        supports_streaming=False,
                        parameters=[],
                        voices=[],
                    )
                    engine_infos.append(engine_info)

            logger.info(f"获取引擎信息完成，共 {len(engine_infos)} 个引擎")
            return engine_infos

        except Exception as e:
            logger.error(f"获取TTS引擎列表失败: {e}")
            raise

    def transfer_parameters(self, raw_parameters) -> Dict[str, TTSParameter]:
        """将引擎的参数配置转换为TTSParameter"""
        logger.debug("转换TTS引擎参数配置")
        parameter_configs = {}
        for param_name, param_config in raw_parameters.items():
            parameter_configs[param_name] = TTSParameter(**param_config)

        return parameter_configs

    async def synthesize_text(
        self,
        engine_type: TTSEngineType,
        voice_id_name: str,
        voice_style_name: str,
        text: str,
        output_path: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ) -> TTSSynthesizeResponse:
        """合成语音"""
        logger.info(
            f"开始语音合成，引擎: {engine_type.value}, 语音: {voice_id_name}, 文本长度: {len(text)}"
        )
        try:
            engine = self.tts_manager.get_engine(engine_type.value)
            if not engine:
                logger.error(f"引擎 {engine_type.value} 不存在或不可用")
                raise ValueError(f"引擎 {engine_type.value} 不存在或不可用")

            # 如果没有指定输出路径，生成一个唯一的文件名
            if not output_path:
                # 生成时分秒时间戳作为文件名
                now = datetime.now()
                time_stamp = now.strftime("%H%M%S%f")[:9]  # Include microseconds
                output_path = f"tts_{engine_type.value}_{time_stamp}.wav"

            output_path = f"tmp/{datetime.now().strftime('%Y-%m-%d')}/{output_path}"
            logger.debug(f"生成的输出路径: {output_path}")

            # 调用引擎合成音频
            if parameters:
                logger.debug(f"使用自定义参数合成: {parameters}")
                audio_data = engine.synthesize(
                    text, voice_id_name, voice_style_name, output_path, **parameters
                )
            else:
                logger.debug("使用默认参数合成")
                audio_data = engine.synthesize(
                    text, voice_id_name, voice_style_name, output_path
                )
            logger.info("语音合成引擎调用成功")

            # 获取文件信息
            file_size = len(audio_data) if audio_data else 0

            # 检查实际的文件路径
            from ..utils.path_utils import resolve_resource_path

            actual_file_path = resolve_resource_path(f"TTS/{output_path}")
            logger.info(f"音频文件保存路径: {actual_file_path}")
            logger.info(f"文件是否存在: {actual_file_path.exists()}")

            if actual_file_path.exists():
                file_size = actual_file_path.stat().st_size
                logger.info(f"文件大小: {file_size} bytes")
            else:
                logger.warning(f"音频文件 {actual_file_path} 未找到或大小为0")

            # 计算音频时长（简单估算，可以后续改进为读取实际音频文件时长）
            # 假设平均语速为每分钟150字符，可以根据实际情况调整
            estimated_duration = len(text) / 150 * 60
            logger.debug(f"估算音频时长: {estimated_duration:.2f} 秒")

            # 获取音频格式
            audio_format = Path(output_path).suffix.lstrip(".").lower()

            logger.info("语音合成完成")
            return TTSSynthesizeResponse(
                success=True,
                message="语音合成成功",
                audio_url=f"/api/voices/{output_path}",
                audio_file_path=f"/resources/TTS/{output_path}",
                duration=estimated_duration,
                file_size=file_size,
                format=audio_format,
                sample_rate=22050,  # 默认采样率，可以从引擎获取实际值
            )

        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return TTSSynthesizeResponse(
                success=False, message=f"语音合成失败: {str(e)}"
            )

    def get_engine_status(self, engine_type: TTSEngineType) -> Dict[str, Any]:
        """获取引擎状态信息"""
        logger.info(f"获取引擎 {engine_type.value} 状态...")
        try:
            engine = self.tts_manager.get_engine(engine_type.value)
            if not engine:
                logger.warning(f"引擎 {engine_type.value} 不存在")
                return {"available": False, "error": f"引擎 {engine_type.value} 不存在"}

            # 尝试初始化引擎来检查状态
            is_initialized = engine.initialize()
            logger.info(f"引擎 {engine_type.value} 初始化状态: {is_initialized}")

            return {
                "available": is_initialized,
                "engine_info": engine.get_engine_info() if is_initialized else None,
            }

        except Exception as e:
            logger.error(f"获取引擎 {engine_type.value} 状态失败: {e}")
            return {"available": False, "error": str(e)}

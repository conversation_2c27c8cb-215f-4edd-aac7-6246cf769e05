import importlib
import inspect
import os
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Set

from .engine import TTSEngineBase

try:
    # 尝试相对导入（正常模块导入时）
    from ..utils.logging_config import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入（测试环境中）
    try:
        from utils.logging_config import get_logger
    except ImportError:
        # 如果都失败，使用标准logging
        import logging

        def get_logger(name: str):
            return logging.getLogger(name)


# 设置日志
logger = get_logger(__name__)


class TTSManager:
    """TTS管理器，统一入口（单例模式）"""

    _instance = None
    _initialized = False

    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(TTSManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化TTS管理器"""
        # 避免重复初始化
        if self._initialized:
            logger.debug("TTSManager already initialized, skipping...")
            return

        self._tts_engines = {}
        self._available_engines = set()  # 只存储可用的引擎
        self._engine_initialization_cache = {}  # 引擎初始化结果缓存
        self._engine_instances_cache = {}  # 引擎实例缓存

        # 启动时直接注册所有引擎
        self._register_engines()

        self._initialized = True
        logger.info(
            f"TTSManager singleton instance initialized with engines: {list(self._available_engines)}"
        )

    def _get_enabled_engines(self) -> Set[str]:
        """从环境变量获取启用的TTS引擎列表"""
        enabled_engines_str = os.getenv("TTS_ENABLED_ENGINES", "edge,index,minimax")
        enabled_engines = {
            engine.strip()
            for engine in enabled_engines_str.split(",")
            if engine.strip()
        }
        logger.info(f"Enabled TTS engines from config: {enabled_engines}")
        return enabled_engines

    def _register_engines(self):
        """动态注册所有TTS引擎"""
        enabled_engines = self._get_enabled_engines()

        # 获取当前包的路径
        current_dir = Path(__file__).parent

        # 遍历所有子目录
        for subdir in current_dir.iterdir():
            if not subdir.is_dir() or subdir.name.startswith("__"):
                continue

            engine_name = subdir.name

            # 检查是否有引擎实现文件 (以 _engine.py 结尾的文件)
            impl_file = subdir / f"{engine_name}_engine.py"
            if not impl_file.exists():
                continue

            # 只注册启用的引擎
            if engine_name not in enabled_engines:
                logger.info(
                    f"Skipping TTS engine '{engine_name}' (not enabled in config)"
                )
                continue

            self._register_single_engine(engine_name, subdir)

    def _register_single_engine(self, engine_name: str, subdir: Path):
        """注册单个TTS引擎"""
        # 如果已经缓存了初始化结果，直接使用
        if engine_name in self._engine_initialization_cache:
            cached_result = self._engine_initialization_cache[engine_name]
            if cached_result["success"]:
                self._tts_engines[engine_name] = cached_result["engine_class"]
                self._available_engines.add(engine_name)
                logger.info(f"Using cached registration for TTS engine: {engine_name}")
            return

        try:
            # 导入模块
            module = self._import_engine_module(engine_name)

            # 查找TTS类
            engine_class = self._find_tts_class(module, engine_name)

            if engine_class:
                # 尝试初始化引擎以验证其可用性
                if self._test_engine_initialization(engine_class, engine_name):
                    self._tts_engines[engine_name] = engine_class
                    self._available_engines.add(engine_name)
                    # 缓存成功的初始化结果
                    self._engine_initialization_cache[engine_name] = {
                        "success": True,
                        "engine_class": engine_class,
                    }
                    logger.info(f"Successfully registered TTS engine: {engine_name}")
                else:
                    # 缓存失败的初始化结果
                    self._engine_initialization_cache[engine_name] = {
                        "success": False,
                        "error": "Initialization test failed",
                    }
                    logger.warning(
                        f"TTS engine '{engine_name}' failed initialization test"
                    )
            else:
                logger.warning(
                    f"No TTSEngineBase subclass found in {engine_name}.tts_impl"
                )

        except (ImportError, AttributeError) as e:
            # 缓存失败的初始化结果
            self._engine_initialization_cache[engine_name] = {
                "success": False,
                "error": str(e),
            }
            logger.warning(f"Failed to load TTS engine '{engine_name}': {e}")

    def _test_engine_initialization(self, engine_class, engine_name: str) -> bool:
        """测试引擎是否能够成功初始化"""
        try:
            # 创建引擎实例进行完全测试，确保启动时就完全初始化
            engine = engine_class()

            # 调用 initialize 方法来真正验证引擎是否可用
            is_initialized = engine.initialize()

            if is_initialized:
                logger.info(f"Engine '{engine_name}' initialization successful")
                return True
            else:
                logger.warning(f"Engine '{engine_name}' initialize() returned False")
                return False

        except Exception as e:
            logger.warning(f"Engine '{engine_name}' initialization failed: {e}")
            return False

    def _import_engine_module(self, engine_name: str):
        """导入TTS引擎模块"""
        module_path = f"{engine_name}.{engine_name}_engine"
        return importlib.import_module(f".{module_path}", package=__package__)

    def _find_tts_class(self, module, engine_name: str):
        """在模块中查找TTS类"""
        # 查找继承自TTSEngineBase的类
        tts_classes = []
        for name, obj in inspect.getmembers(module, inspect.isclass):
            # 检查是否继承自TTSEngineBase且不是TTSEngineBase本身
            if (
                issubclass(obj, TTSEngineBase)
                and obj != TTSEngineBase
                and obj.__module__ == module.__name__
            ):
                tts_classes.append((name, obj))

        if not tts_classes:
            return None

        # 如果找到多个类，优先选择名称匹配 {engine_name}TTS 的类
        expected_class_name = f"{engine_name.capitalize()}TTS"

        for class_name, class_obj in tts_classes:
            if class_name == expected_class_name:
                return class_obj

        # 如果没有找到预期的类名，使用第一个找到的类
        engine_class = tts_classes[0][1]
        logger.warning(
            f"Using class '{tts_classes[0][0]}' for engine '{engine_name}', expected '{expected_class_name}'"
        )
        return engine_class

    def get_engine(
        self, engine_name: str, config: Optional[Dict[str, Any]] = None
    ) -> TTSEngineBase:
        """获取TTS引擎实例"""
        if engine_name not in self._available_engines:
            available = list(self._available_engines)
            raise ValueError(
                f"TTS engine '{engine_name}' is not available. Available engines: {available}"
            )

        # 使用实例缓存，避免重复创建引擎实例
        cache_key = f"{engine_name}_{hash(str(sorted((config or {}).items())))}"

        if cache_key in self._engine_instances_cache:
            logger.debug(f"使用缓存的引擎实例: {engine_name}")
            return self._engine_instances_cache[cache_key]

        engine_class = self._tts_engines[engine_name]
        engine_instance = engine_class(config)

        # 缓存引擎实例
        self._engine_instances_cache[cache_key] = engine_instance
        logger.info(f"创建并缓存引擎实例: {engine_name}")

        return engine_instance

    def list_available_engines(self) -> list:
        """列出所有可用的TTS引擎"""
        return list(self._available_engines)

    def is_engine_available(self, engine_name: str) -> bool:
        """检查指定引擎是否可用"""
        return engine_name in self._available_engines

    def synthesize(
        self,
        text: str,
        engine_name: str,
        voice_id_name: str,
        voice_style_name: str,
        output_path: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> bytes:
        """使用指定引擎合成语音"""
        engine = self.get_engine(engine_name, config)
        return engine.synthesize(
            text, voice_id_name, voice_style_name, output_path, **kwargs
        )

    def synthesize_stream(
        self,
        text: str,
        engine_name: str,
        voice_id_name: str,
        voice_style_name: str,
        config: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> Iterator[bytes]:
        """使用指定引擎流式合成语音"""
        engine = self.get_engine(engine_name, config)
        return engine.synthesize_stream(text, voice_id_name, voice_style_name, **kwargs)

    def get_models(
        self, engine_name: str, config: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """获取指定TTS引擎的模型列表"""
        engine = self.get_engine(engine_name, config)
        engine_info = engine.get_engine_info()
        return engine_info.get("models", [])

    def list_all_models(self) -> Dict[str, List[str]]:
        """列出所有TTS引擎的模型"""
        all_models = {}
        for engine_name in self._available_engines:
            try:
                engine = self.get_engine(engine_name)
                engine_info = engine.get_engine_info()
                all_models[engine_name] = engine_info.get("models", [])
            except Exception as e:
                logger.warning(f"Failed to get models for engine '{engine_name}': {e}")
                all_models[engine_name] = []
        return all_models


# 便捷函数
def create_tts_manager() -> TTSManager:
    """获取TTS管理器单例实例"""
    return TTSManager()


def synthesize_text(
    text: str,
    engine: str = "edge",
    voice_id_name: str = "",
    voice_style_name: str = "",
    output_path: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None,
    **kwargs,
) -> bytes:
    """快速合成文本为语音"""
    manager = create_tts_manager()
    return manager.synthesize(
        text, engine, voice_id_name, voice_style_name, output_path, config, **kwargs
    )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统初始化模块
负责系统启动时的各种初始化操作
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv


class SystemInitializer:
    """系统初始化器"""

    def __init__(self):
        self.logger: Optional[logging.Logger] = None
        self.tts_manager = None
        self._initialized = False

    def initialize(self) -> bool:
        """执行完整的系统初始化

        Returns:
            bool: 初始化是否成功
        """
        try:
            # 1. 加载环境变量
            self._load_environment()

            # 2. 配置日志系统
            self._setup_logging()

            # 3. 初始化TTS管理器
            self._initialize_tts_manager()

            # 4. 验证关键组件
            self._validate_components()

            self._initialized = True
            self.logger.info("系统初始化完成")
            return True

        except Exception as e:
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}")
            else:
                logger.error(
                    f"系统初始化失败（日志系统未完全配置）: {e}"
                )  # Fallback to direct logger if self.logger isn't ready
            return False

    def _load_environment(self):
        """加载环境变量"""
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent.parent
        env_file = project_root / ".env"

        # 加载环境变量文件
        if env_file.exists():
            logger.info(f"已加载环境变量文件: {env_file}")
        else:
            load_dotenv()
            logger.warning(f"环境变量文件不存在: {env_file}，尝试从默认位置加载")
        # for key, value in os.environ.items():
        #     print(f"==={key}: {value}")

        # 设置默认环境变量
        self._set_default_env_vars()

    def _set_default_env_vars(self):
        """设置默认环境变量"""
        defaults = {
            "TTS_ENABLED_ENGINES": "edge,index,minimax",
            "LOG_LEVEL": "INFO",
            "LOG_FILE": "resources/logs/app.log",
            "INDEX_TTS_MODEL_PATH": "resources/TTS/index-tts/checkpoints",
            "INDEX_TTS_CONFIG_PATH": "resources/TTS/index-tts/checkpoints/config.yaml",
            "INDEX_TTS_PRECISION": "fp16",
            "INDEX_TTS_DEVICE": "auto",
            "INDEX_TTS_USE_CUDA_KERNEL": "false",
        }

        for key, value in defaults.items():
            if not os.getenv(key):
                os.environ[key] = value
                logger.debug(f"设置默认环境变量: {key}={value}")
            else:
                logger.debug(f"环境变量 '{key}' 已存在: {os.getenv(key)}")

    def _setup_logging(self):
        """配置日志系统"""
        try:
            # 尝试使用项目的日志配置
            from ..utils.logging_config import get_logger, setup_logging
            from ..utils.path_utils import ensure_dir_exists, resolve_path

            # 获取日志配置
            log_level = os.getenv("LOG_LEVEL", "INFO").upper()
            log_file = os.getenv("LOG_FILE", "resources/logs/app.log")

            # 使用path_utils解析日志文件路径
            log_file_path = resolve_path(log_file)

            # 确保日志目录存在
            ensure_dir_exists(log_file_path.parent)

            # 配置日志系统
            setup_logging(
                level=log_level,
                log_file=str(log_file_path),
                console_output=True,
            )

            # >> 新增：强制设置第三方库的日志级别
            logging.getLogger("litellm").setLevel(logging.WARNING)
            logging.getLogger("langsmith").setLevel(logging.WARNING)
            logging.getLogger("urllib3").setLevel(logging.WARNING)
            # << 新增结束

            # 获取logger
            self.logger = get_logger(__name__)
            self.logger.info("项目日志系统配置成功，第三方库日志级别已设为WARNING")

        except ImportError:
            # 如果导入失败，使用基本日志配置
            self._setup_basic_logging()
            self.logger.warning("无法导入项目日志配置，使用基本日志系统")
        except Exception as e:
            # 这里不能用logger，因为日志可能还没完全配置好
            print(f"严重错误：配置日志系统失败，将使用基本打印: {e}")
            # Fallback to basic print if even basic logging setup fails before logger is fully ready
            self._setup_basic_logging()  # Re-attempt basic setup in case of prior partial failure
            if (
                self.logger
            ):  # Check if self.logger was successfully initialized by basic setup
                self.logger.error(f"配置项目日志系统失败: {e}")
            else:
                # If basic setup also failed to initialize logger, just print
                print(f"致命错误：日志系统初始化失败，无法记录日志: {e}")

    def _setup_basic_logging(self):
        """设置基本日志配置"""
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        log_file = os.getenv("LOG_FILE", "resources/logs/app.log")

        try:
            # 尝试使用path_utils处理路径
            from ..utils.path_utils import ensure_dir_exists, resolve_path

            log_file_path = resolve_path(log_file)
            ensure_dir_exists(log_file_path.parent)
            log_file = str(log_file_path)
            logger.debug(f"基本日志：使用path_utils处理日志文件路径: {log_file}")
        except ImportError:
            logger.warning("无法导入path_utils，使用基本路径处理日志文件")
            # 如果path_utils不可用，使用基本路径处理
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent.parent
            log_file_path = project_root / log_file
            log_file_path.parent.mkdir(parents=True, exist_ok=True)
            log_file = str(log_file_path)
        except Exception as e:
            logger.error(f"基本日志：处理日志文件路径失败: {e}")
            # Fallback to current working directory if all else fails
            log_file = Path("app.log").resolve()  # Fallback to a safe default
            log_file.parent.mkdir(parents=True, exist_ok=True)
            log_file = str(log_file)
            logger.warning(f"基本日志：日志文件路径处理失败，使用默认路径: {log_file}")

        # 配置日志格式
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level, logging.INFO))

        # 清除现有的处理器
        root_logger.handlers.clear()

        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 添加文件处理器
        try:
            file_handler = logging.FileHandler(log_file, encoding="utf-8")
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            logger.info(f"基本日志系统配置成功，日志输出到控制台和文件: {log_file}")
        except Exception as e:
            logger.error(f"无法创建日志文件处理器，日志将只输出到控制台: {e}")

        self.logger = logging.getLogger(__name__)

    def _initialize_tts_manager(self):
        """初始化TTS管理器"""
        try:
            from ..tts.manager import create_tts_manager

            self.logger.info("正在初始化TTS管理器...")
            self.tts_manager = create_tts_manager()

            # 记录已注册的引擎
            available_engines = self.tts_manager.list_available_engines()
            self.logger.info(f"可用的TTS引擎: {available_engines}")

            if not available_engines:
                self.logger.warning("没有可用的TTS引擎")

        except Exception as e:
            self.logger.error(f"TTS管理器初始化失败: {e}")
            raise

    def _validate_components(self):
        """验证关键组件"""
        self.logger.info("开始验证关键系统组件")
        # 验证TTS管理器
        if self.tts_manager is None:
            self.logger.critical("TTS管理器未初始化，系统无法正常工作")
            raise RuntimeError("TTS管理器未初始化")

        # 验证至少有一个可用的TTS引擎
        available_engines = self.tts_manager.list_available_engines()
        if not available_engines:
            self.logger.warning("没有可用的TTS引擎，某些功能可能无法正常工作")

        self.logger.info("组件验证完成")

    def get_tts_manager(self):
        """获取TTS管理器实例"""
        if not self._initialized:
            raise RuntimeError("系统尚未初始化")
        return self.tts_manager

    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self._initialized


# 全局系统初始化器实例
_system_initializer: Optional[SystemInitializer] = None

# 在文件加载时就初始化 logger，以便在 SystemInitializer 实例创建前也能记录日志
from ..utils.logging_config import get_logger

logger = get_logger(__name__)


def get_system_initializer() -> SystemInitializer:
    """获取系统初始化器实例（单例模式）"""
    global _system_initializer
    if _system_initializer is None:
        logger.info("创建 SystemInitializer 实例")
        _system_initializer = SystemInitializer()
    return _system_initializer


def initialize_system() -> bool:
    """初始化系统（便捷函数）"""
    logger.info("调用系统初始化便捷函数")
    initializer = get_system_initializer()
    return initializer.initialize()


def get_tts_manager():
    """获取TTS管理器实例（便捷函数）"""
    logger.debug("调用获取TTS管理器便捷函数")
    initializer = get_system_initializer()
    return initializer.get_tts_manager()


def is_system_initialized() -> bool:
    """检查系统是否已初始化（便捷函数）"""
    global _system_initializer
    if _system_initializer is None:
        logger.debug("系统初始化器未创建，系统未初始化")
        return False
    return _system_initializer.is_initialized()

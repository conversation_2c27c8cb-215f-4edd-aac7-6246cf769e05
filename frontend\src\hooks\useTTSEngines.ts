import { useState, useEffect } from 'react';
import { ttsApi } from '../services/ttsApi';
import type { TTSEngine, VoiceOption } from '../types/tts';

interface UseTTSEnginesResult {
  engines: TTSEngine[];
  voices: VoiceOption[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getEngineById: (id: string) => TTSEngine | undefined;
  getVoicesByEngine: (engineId: string) => VoiceOption[];
}

export function useTTSEngines(): UseTTSEnginesResult {
  const [engines, setEngines] = useState<TTSEngine[]>([]);
  const [voices, setVoices] = useState<VoiceOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEngines = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const engineData = await ttsApi.getAvailableEngines();
      setEngines(engineData);

      // 转换声音数据格式
      const allVoices: VoiceOption[] = [];
      engineData.forEach((engine) => {
        engine.voices.forEach((voice) => {
          allVoices.push({
            id: voice.id_name,
            name: voice.display_name,
            gender: voice.gender.toLowerCase() as 'male' | 'female',
            description: voice.description,
            language: voice.locale,
            styles: Object.keys(voice.styles),
          });
        });
      });

      setVoices(allVoices);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : '获取引擎数据失败';
      setError(errorMessage);
      console.error('Failed to fetch TTS engines:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getEngineById = (id: string): TTSEngine | undefined => {
    return engines.find((engine) => engine.id === id);
  };

  const getVoicesByEngine = (engineId: string): VoiceOption[] => {
    const engine = getEngineById(engineId);
    if (!engine) return [];

    return engine.voices.map((voice) => ({
      id: voice.id_name,
      name: voice.display_name,
      gender: voice.gender.toLowerCase() as 'male' | 'female',
      description: voice.description,
      language: voice.locale,
      styles: Object.keys(voice.styles),
    }));
  };

  useEffect(() => {
    fetchEngines();
  }, []);

  return {
    engines,
    voices,
    isLoading,
    error,
    refetch: fetchEngines,
    getEngineById,
    getVoicesByEngine,
  };
}

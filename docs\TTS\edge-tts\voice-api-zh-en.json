[{"id_name": "zh-HK-HiuGaaiNeural", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Neur<PERSON>（女）-中文-香港", "lang": "zh", "locale": "zh-HK"}, {"id_name": "zh-HK-HiuMaanNeural", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON>aanNeur<PERSON>（女）-中文-香港", "lang": "zh", "locale": "zh-HK"}, {"id_name": "zh-HK-WanLungNeural", "gender": "Male", "display_name": "WanLungNeural（男）-中文-香港", "lang": "zh", "locale": "zh-HK"}, {"id_name": "zh-CN-XiaoxiaoNeural", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON>N<PERSON><PERSON>（女）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-XiaoyiNeural", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>（女）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-YunjianNeural", "gender": "Male", "display_name": "YunjianNeural（男）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-YunxiNeural", "gender": "Male", "display_name": "YunxiNeural（男）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-YunxiaNeural", "gender": "Male", "display_name": "YunxiaNeural（男）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-YunyangNeural", "gender": "Male", "display_name": "YunyangNeural（男）-中文-大陆", "lang": "zh", "locale": "zh-CN"}, {"id_name": "zh-CN-liaoning-XiaobeiNeural", "gender": "Female", "display_name": "XiaobeiNeural（女）-中文-辽宁", "lang": "zh", "locale": "zh-CN-liaoning"}, {"id_name": "zh-TW-HsiaoChenNeural", "gender": "Female", "display_name": "HsiaoChenNeural（女）-中文-台湾", "lang": "zh", "locale": "zh-TW"}, {"id_name": "zh-TW-YunJheNeural", "gender": "Male", "display_name": "Yun<PERSON>heNeural（男）-中文-台湾", "lang": "zh", "locale": "zh-TW"}, {"id_name": "zh-TW-HsiaoYuNeural", "gender": "Female", "display_name": "HsiaoYuNeural（女）-中文-台湾", "lang": "zh", "locale": "zh-TW"}, {"id_name": "zh-CN-shaanxi-XiaoniNeural", "gender": "Female", "display_name": "XiaoniNeur<PERSON>（女）-中文-陕西", "lang": "zh", "locale": "zh-CN-shaanxi"}, {"id_name": "en-US-AvaNeural", "gender": "Female", "display_name": "AvaNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "AndrewNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Female", "display_name": "EmmaNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "BrianNeur<PERSON>（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-GB-LibbyNeural", "gender": "Female", "display_name": "Libby<PERSON>eural（女）-英语-英国", "lang": "en", "locale": "en-GB"}, {"id_name": "en-GB-<PERSON><PERSON><PERSON>", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>（女）-英语-英国", "lang": "en", "locale": "en-GB"}, {"id_name": "en-GB-RyanN<PERSON><PERSON>", "gender": "Male", "display_name": "RyanNeural（男）-英语-英国", "lang": "en", "locale": "en-GB"}, {"id_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "gender": "Female", "display_name": "Sonia<PERSON><PERSON><PERSON>（女）-英语-英国", "lang": "en", "locale": "en-GB"}, {"id_name": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "ThomasNeural（男）-英语-英国", "lang": "en", "locale": "en-GB"}, {"id_name": "en-US-AnaNeural", "gender": "Female", "display_name": "AnaNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-AndrewMultilingualNeural", "gender": "Male", "display_name": "AndrewMultilingualNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-AriaNeural", "gender": "Female", "display_name": "AriaNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-AvaMultilingualNeural", "gender": "Female", "display_name": "AvaMultilingualNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-BrianMultilingualNeural", "gender": "Male", "display_name": "BrianMultilingualNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "ChristopherNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-EmmaMultilingualNeural", "gender": "Female", "display_name": "EmmaMultilingualNeural（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "EricNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-GuyN<PERSON><PERSON>", "gender": "Male", "display_name": "GuyNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON><PERSON>（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON>", "gender": "Female", "display_name": "<PERSON><PERSON><PERSON><PERSON>（女）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "RogerNeural（男）-英语-美国", "lang": "en", "locale": "en-US"}, {"id_name": "en-US-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gender": "Male", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>（男）-英语-美国", "lang": "en", "locale": "en-US"}]
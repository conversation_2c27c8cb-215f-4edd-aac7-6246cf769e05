#!/usr/bin/env python3
"""
测试改进的format接口
使用提供的小说样例进行测试
"""

import os
import sys

import requests

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_format_api():
    """测试format接口的新功能"""

    # 读取测试小说内容
    test_novels = {
        "彩票纠纷": "../小说/彩票纠纷.txt",
        "倚天": "../小说/倚天.txt",
        "学委抢了贫困生名额后": "../小说/学委抢了贫困生名额后.txt",
    }

    base_url = "http://localhost:8000/api/text"

    for novel_name, file_path in test_novels.items():
        print(f"\n{'=' * 50}")
        print(f"测试小说: {novel_name}")
        print(f"{'=' * 50}")

        try:
            # 读取小说内容
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 测试format接口
            format_request = {
                "content": content[:2000],  # 只取前2000字符进行测试
                "enable_chapter_detection": True,
                "enable_dialogue_formatting": True,
                "enable_character_preprocessing": True,
                "normalize_quotes": True,
                "novel_type": "auto",
                "preserve_chapter_numbers": True,
            }

            print("📝 调用format接口...")
            response = requests.post(f"{base_url}/format", json=format_request)

            if response.status_code == 200:
                result = response.json()
                print("✅ 格式化成功!")
                print(
                    f"   章节数量: {result.get('chapter_count', 0)}"
                )
                print(f"   章节数量: {result.get('chapter_count', 0)}")
                print(f"   处理时间: {result.get('processing_time', 0):.3f}秒")

                if result.get("format_statistics"):
                    stats = result["format_statistics"]
                    print(f"   对话片段: {stats.get('dialogue_count', 0)}")
                    print(f"   叙述片段: {stats.get('narration_count', 0)}")
                    print(f"   角色提示: {stats.get('character_hints_count', 0)}")

                if result.get("character_hints"):
                    print(f"   潜在角色: {', '.join(result['character_hints'])}")

                # 显示前几个章节信息
                if result.get("chapters"):
                    print("\n📚 章节信息:")
                    for i, chapter in enumerate(result["chapters"][:3]):
                        print(
                            f"   第{chapter['chapter_index']}章: {chapter['title'][:30]}..."
                        )
                        print(
                            f"     字数: {chapter['word_count']}, 对话数: {chapter['dialogue_count']}"
                        )

                # 显示前几个对话片段
                if result.get("dialogue_segments"):
                    print("\n💬 对话片段示例:")
                    for i, segment in enumerate(result["dialogue_segments"][:5]):
                        seg_type = segment["segment_type"]
                        content = (
                            segment["content"][:50] + "..."
                            if len(segment["content"]) > 50
                            else segment["content"]
                        )
                        character = segment.get("character_hint", "未知")
                        print(f"   {i + 1}. [{seg_type}] {character}: {content}")

            else:
                print(f"❌ 格式化失败: {response.status_code}")
                print(f"   错误信息: {response.text}")

            # 测试角色提取接口
            print("\n🎭 测试角色提取...")
            character_request = {
                "content": content[:1500],  # 取前1500字符
                "use_ai": False,  # 暂时不使用AI
                "novel_type": "auto",
                "max_characters": 8,
            }

            response = requests.post(
                f"{base_url}/extract-characters", json=character_request
            )

            if response.status_code == 200:
                result = response.json()
                print("✅ 角色提取成功!")
                print(f"   处理时间: {result.get('processing_time', 0):.3f}秒")
                print(f"   总对话数: {result.get('dialogue_count', 0)}")

                if result.get("characters"):
                    print("   角色列表:")
                    for char in result["characters"]:
                        print(
                            f"     - {char['name']} ({char['gender']}) [{char['role_type']}]"
                        )
                        print(f"       对话次数: {char.get('dialogue_count', 0)}")
            else:
                print(f"❌ 角色提取失败: {response.status_code}")
                print(f"   错误信息: {response.text}")

            # 测试文本分割接口
            print("\n✂️ 测试文本分割...")
            split_request = {
                "content": content[:1000],
                "split_type": "dialogue",
                "max_length": 200,
                "min_length": 20,
            }

            response = requests.post(f"{base_url}/split", json=split_request)

            if response.status_code == 200:
                result = response.json()
                print("✅ 文本分割成功!")
                print(f"   处理时间: {result.get('processing_time', 0):.3f}秒")
                print(f"   分割片段数: {result.get('total_segments', 0)}")
                print(f"   原始长度: {result.get('original_length', 0)}")

                if result.get("segments"):
                    print("   片段示例:")
                    for i, segment in enumerate(result["segments"][:3]):
                        content_preview = (
                            segment["content"][:40] + "..."
                            if len(segment["content"]) > 40
                            else segment["content"]
                        )
                        print(
                            f"     {i + 1}. [{segment['segment_type']}] {content_preview}"
                        )
            else:
                print(f"❌ 文本分割失败: {response.status_code}")
                print(f"   错误信息: {response.text}")

        except FileNotFoundError:
            print(f"❌ 文件未找到: {file_path}")
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")


if __name__ == "__main__":
    print("🚀 开始测试改进的小说格式化接口")
    print("请确保后端服务已启动 (http://localhost:8000)")

    try:
        # 测试服务器连接
        response = requests.get("http://localhost:8000/api/text/format/help")
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            test_format_api()
        else:
            print("❌ 服务器连接失败")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务已启动")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

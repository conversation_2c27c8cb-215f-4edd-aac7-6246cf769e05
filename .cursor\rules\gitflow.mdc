---
description: Gitflow Workflow Rules. These rules should be applied when performing git operations.
---
# Gitflow 工作流规则

## 主要分支

### main
- 包含生产就绪的代码
- 切勿直接提交到 main 分支
- 只接受来自以下分支的合并：
  - hotfix/* 分支
  - release/* 分支
- 每次合并后必须打上版本号标签

### develop
- 主要开发分支
- 包含最新的已交付开发变更
- 功能分支的源分支
- 切勿直接提交到 develop 分支

## 支持分支

### feature/*
- 分支来源：develop
- 合并回：develop
- 命名约定：feature/[问题ID]-描述性名称
- 示例：feature/123-user-authentication
- 创建 PR 前必须与 develop 分支保持最新
- 合并后删除

### release/*
- 分支来源：develop
- 合并回：
  - main
  - develop
- 命名约定：release/vX.Y.Z
- 示例：release/v1.2.0
- 仅限错误修复、文档和面向发布的任务
- 无新功能
- 合并后删除

### hotfix/*
- 分支来源：main
- 合并回：
  - main
  - develop
- 命名约定：hotfix/vX.Y.Z
- 示例：hotfix/v1.2.1
- 仅限紧急的生产修复
- 合并后删除

## 提交信息

- 格式：`类型(范围): 描述`
- 类型：
  - feat: 新功能
  - fix: 错误修复
  - docs: 文档变更
  - style: 格式化、缺少分号等
  - refactor: 代码重构
  - test: 添加测试
  - chore: 日常维护任务

## 版本控制

### 语义化版本
- 主版本号 (MAJOR) 用于不兼容的 API 变更
- 次版本号 (MINOR) 用于向后兼容的功能添加
- 修订号 (PATCH) 用于向后兼容的错误修复

## Pull Request 规则
1. 所有变更必须通过 Pull Request
2. 需要审批：至少 1 人
3. CI 检查必须通过
4. 禁止直接提交到受保护分支 (main, develop)
5. 合并前分支必须是最新状态
6. 合并后删除分支

## 分支保护规则

### main & develop
- 要求 Pull Request 审查
- 要求状态检查通过
- 要求分支是最新状态
- 将管理员纳入限制范围
- 禁止强制推送
- 禁止删除

## 发布流程
1. 从 develop 分支创建 release 分支
2. 更新版本号
3. 修复任何特定于版本的问题
4. 创建到 main 分支的 PR
5. 合并到 main 分支后：
   - 打上发布标签
   - 合并回 develop 分支
   - 删除 release 分支

## Hotfix 流程
1. 从 main 分支创建 hotfix 分支
2. 修复问题
3. 更新修订版本号
4. 创建到 main 分支的 PR
5. 合并到 main 分支后：
   - 打上发布标签
   - 合并回 develop 分支
   - 删除 hotfix 分支
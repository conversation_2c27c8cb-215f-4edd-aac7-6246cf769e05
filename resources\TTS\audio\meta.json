[{"id_name": "zh-CN-Yunxi", "gender": "Male", "display_name": "云希-普通话", "locale": "zh-CN", "age_group": "青年", "tts_engine": "index-tts", "description": "声音如跳跃的夏日阳光，充满蓬勃的生命力与少年感，适合小说中朝气蓬勃的年轻角色", "styles": {"通用": "YunXi/zh-CN-YunXi-General.wav", "旁白": "YunXi/zh-CN-Yunxi-Narration-relaxed.wav", "尴尬": "YunXi/zh-CN-YunXi-Embarrassed.wav", "担心害怕": "YunXi/zh-CN-YunXi-Fearful.wav", "愉悦": "YunXi/zh-CN-YunXi-Cheerful.wav", "不满": "YunXi/zh-CN-YunXi-Disgruntled.wav", "严肃庄重": "YunXi/zh-CN-YunXi-Serious.wav", "愤怒": "YunXi/zh-CN-Yun<PERSON>i-Angry.wav", "伤心": "YunXi/zh-CN-YunXi-sad.wav", "沮丧": "YunXi/zh-CN-YunXi-Depressed.wav", "助手": "YunXi/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Assistant.wav", "新闻播报": "YunXi/zh-CN-Yunxi-Newscast.wav"}, "exa": {}}, {"id_name": "zh-CN-Yunjian", "gender": "Male", "display_name": "云健-普通话", "locale": "zh-CN", "age_group": "中年", "tts_engine": "index-tts", "description": "一种深沉、随意且有吸引力的声音，将权威性与亲和力相结合，让对话变得轻松自在。", "styles": {"默认": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-Audio.wav", "旁白（放松）": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-Narration-relaxed-Audio.wav", "体育解说": "<PERSON><PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Sports_commentary-Audio.wav", "体育解说（兴奋）": "<PERSON><PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Sports_commentary_excited-Audio.wav", "愤怒": "<PERSON><PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Angry-Audio.wav", "不满": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-<PERSON>sgruntled-Audio.wav", "伤心": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-Sad-Audio.wav", "严肃庄重": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-Serious-Audio.wav", "沮丧": "Yunjian/zh-C<PERSON>-<PERSON><PERSON><PERSON>-Depressed-Audio.wav", "旁白": "<PERSON><PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Documentary-narration-Audio.wav"}, "exa": {}}, {"id_name": "zh-CN-Yunxia", "gender": "Male", "display_name": "云夏-普通话", "locale": "zh-CN", "age_group": "少年", "tts_engine": "index-tts", "description": "声音清澈明亮，带着少年特有的活力与朝气，适合小说中阳光开朗的年轻角色", "styles": {"通用": "Yunxia/zh-C<PERSON>-<PERSON><PERSON>-General-Audio.wav", "愉悦": "Yunxia/zh-CN-<PERSON><PERSON>-Cheerful-Audio.wav", "伤心": "Yunxia/zh-CN-Yunxia-Sad-Audio.wav", "愤怒": "Yunxia/zh-CN-<PERSON><PERSON>-Angry-Audio.wav"}, "exa": {}}, {"id_name": "zh-CN-Yunyang", "gender": "Male", "display_name": "云扬-普通话", "locale": "zh-CN", "age_group": "青年", "tts_engine": "index-tts", "description": "温暖沉稳的男声，兼具自然流畅的韵律与亲和力，适合小说中稳重可靠的男性角色", "styles": {"旁白": "Yunyang/zh-CN-Yun<PERSON>-Narration-professional-Audio.wav"}, "exa": {}}, {"id_name": "zh-CN-Xiaochen", "gender": "Female", "display_name": "晓辰-普通话", "locale": "zh-CN", "age_group": "中年", "tts_engine": "index-tts", "description": "温柔知性的女声，蕴含柔和舒缓的语调与典雅气质，适合诠释故事里聪慧温婉的女性形象", "styles": {"通用": "<PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON>-General-Audio.wav", "直播": "Xiaochen/zh-<PERSON><PERSON>-<PERSON><PERSON>-Livecommercial-Audio.wav"}, "exa": {}}, {"id_name": "zh-CN-<PERSON><PERSON><PERSON>", "gender": "Female", "display_name": "晓晓-普通话", "locale": "zh-CN", "age_group": "青年", "tts_engine": "index-tts", "description": "语气温暖亲切，带有少年特有的柔和感", "styles": {"通用": "Xiao<PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-General-Audio.wav", "愉悦": "Xiaoxiao/zh-C<PERSON>-<PERSON><PERSON><PERSON><PERSON>-Cheerful-Audio.wav", "伤心": "Xiaoxiao/zh-C<PERSON>-<PERSON><PERSON><PERSON><PERSON>-Sad-Audio.wav", "愤怒": "Xiao<PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>-Angry-Audio.wav", "担心害怕": "Xiaoxiao/zh-C<PERSON>-<PERSON><PERSON><PERSON><PERSON>-Fearful-Audio.wav", "兴奋": "Xiaoxiao/zh-CN-<PERSON><PERSON><PERSON><PERSON>-Excited-Audio.wav", "抱歉": "Xiaoxiao/zh-C<PERSON>-<PERSON><PERSON><PERSON>-Sorry-Audio.wav"}, "exa": {}}, {"id_name": "zh-CN-Xiaoyi", "gender": "Female", "display_name": "晓伊-普通话", "locale": "zh-CN", "age_group": "少女", "tts_engine": "index-tts", "description": "语调轻柔带点怯生生的感觉，展现少女的青涩与害羞", "styles": {"通用": "<PERSON><PERSON>/zh-<PERSON><PERSON>-<PERSON><PERSON>-general-Audio.wav", "愉悦": "<PERSON>yi/zh-<PERSON><PERSON>-<PERSON><PERSON>-cheerful-Audio.wav", "伤心": "Xiaoyi/zh-C<PERSON>-<PERSON><PERSON>-sad-Audio.wav", "愤怒": "<PERSON>yi/zh-<PERSON><PERSON>-<PERSON><PERSON>-angry-Audio.wav", "affectionate": "<PERSON>yi/zh-<PERSON><PERSON>-<PERSON><PERSON>-affectionate-Audio.wav", "不满": "<PERSON>yi/zh-<PERSON><PERSON>-<PERSON><PERSON>-disgruntled-Audio.wav"}, "exa": {}}]
#!/usr/bin/env python3
import logging
import sys
from pathlib import Path

# 添加 backend/src 到 Python 路径
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

print("=== 日志测试 ===")

# 设置基本日志配置
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# 配置根日志器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
root_logger.handlers.clear()

# 添加控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)
root_logger.addHandler(console_handler)

print("日志配置完成")

# 测试不同的日志记录器
print("\n--- 测试不同的日志记录器 ---")

# 根日志记录器
root_logger.info("这是根日志记录器的消息")

# 系统初始化日志记录器
system_logger = logging.getLogger("core.system_init")
system_logger.info("这是系统初始化日志记录器的消息")

# IndexTTS 日志记录器
index_logger = logging.getLogger("tts.index.tts_impl")
index_logger.info("这是 IndexTTS 日志记录器的消息")

# 测试 IndexTTS 实际初始化
print("\n--- 测试 IndexTTS 实际初始化 ---")

try:
    from tts.index.tts_impl import IndexTTS

    engine = IndexTTS()
    print("IndexTTS 创建成功")
except Exception as e:
    print(f"IndexTTS 创建失败: {e}")
    import traceback

    traceback.print_exc()

## 📖 故障转移机制 (Fallback System)

### 🎯 **Primary Model（主模型）**
- **定义**: 每个任务类型的**首选模型**
- **作用**: 系统会**优先尝试**使用这个模型来完成任务
- **选择原则**: 通常基于**质量**、**性价比**、**可靠性**来选择

### 🔄 **Fallback Models（备用模型）**
- **定义**: 当主模型失败时的**备选方案**
- **作用**: 按顺序逐一尝试，直到有模型成功
- **触发条件**: 主模型出现以下情况时触发：
  - API调用失败
  - 网络超时
  - 模型服务不可用
  - 返回格式错误

## 🔄 工作流程

```mermaid
graph TD
    A[开始任务] --> B[尝试主模型]
    B --> C{调用成功?}
    C -->|是| D[返回结果]
    C -->|否| E[尝试第1个备用模型]
    E --> F{调用成功?}
    F -->|是| D
    F -->|否| G[尝试第2个备用模型]
    G --> H{调用成功?}
    H -->|是| D
    H -->|否| I[所有模型都失败]
```

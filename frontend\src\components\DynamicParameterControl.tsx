import type { TTSParameter } from '../types/tts';
import Slider from './Slider';

interface DynamicParameterControlProps {
  /** 参数名 */
  paramName: string;
  /** 参数配置 */
  config: TTSParameter;
  /** 当前值 */
  value: any;
  /** 值变化回调 */
  onChange: (paramName: string, value: any) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

export default function DynamicParameterControl({
  paramName,
  config,
  value,
  onChange,
  disabled = false,
}: DynamicParameterControlProps) {
  const handleChange = (newValue: any) => {
    onChange(paramName, newValue);
  };

  // 根据控件类型渲染不同的UI组件
  const renderControl = () => {
    switch (config.control) {
      case 'slider':
        if (config.type === 'int' || config.type === 'float') {
          const [min, max] = config.range as number[];
          return (
            <Slider
              label={config.label}
              value={value || config.default}
              min={min}
              max={max}
              step={config.step || (config.type === 'int' ? 1 : 0.1)}
              unit={config.unit || ''}
              onChange={handleChange}
            />
          );
        }
        break;

      case 'switch':
        if (config.type === 'bool') {
          return (
            <fieldset className="fieldset">
              <label className="label cursor-pointer">
                <span>{config.label}</span>
                <input
                  type="checkbox"
                  className="toggle toggle-primary"
                  checked={value !== undefined ? value : config.default}
                  onChange={(e) => handleChange(e.target.checked)}
                  disabled={disabled}
                />
              </label>
              {config.description && (
                <div className="label">
                  <span className="text-sm text-gray-500">
                    {config.description}
                  </span>
                </div>
              )}
            </fieldset>
          );
        }
        break;

      case 'select':
        if (config.type === 'select' || config.type === 'str') {
          const options = config.range as string[];
          return (
            <fieldset className="fieldset">
              <label className="label">
                <span>{config.label}</span>
              </label>
              <select
                className="select w-full"
                value={value || config.default}
                onChange={(e) => handleChange(e.target.value)}
                disabled={disabled}
              >
                {options?.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
              {config.description && (
                <div className="label">
                  <span className="text-sm text-gray-500">
                    {config.description}
                  </span>
                </div>
              )}
            </fieldset>
          );
        }
        break;

      case 'input':
        return (
          <fieldset className="fieldset">
            <label className="label">
              <span>{config.label}</span>
            </label>
            <input
              type={
                config.type === 'int' || config.type === 'float'
                  ? 'number'
                  : 'text'
              }
              className="input w-full"
              value={value || config.default}
              onChange={(e) => {
                const newValue =
                  config.type === 'int'
                    ? parseInt(e.target.value) || config.default
                    : config.type === 'float'
                      ? parseFloat(e.target.value) || config.default
                      : e.target.value;
                handleChange(newValue);
              }}
              disabled={disabled}
              step={config.step}
              min={
                config.type === 'int' || config.type === 'float'
                  ? (config.range as number[])?.[0]
                  : undefined
              }
              max={
                config.type === 'int' || config.type === 'float'
                  ? (config.range as number[])?.[1]
                  : undefined
              }
            />

            {config.description && (
              <div className="label">
                <span className="text-sm text-gray-500">
                  {config.description}
                </span>
              </div>
            )}
          </fieldset>
        );

      default:
        return (
          <div className="alert alert-warning">
            <span>不支持的控件类型: {config.control}</span>
          </div>
        );
    }
  };

  return <div className="mb-4">{renderControl()}</div>;
}

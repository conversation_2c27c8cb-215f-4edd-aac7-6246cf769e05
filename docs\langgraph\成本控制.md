这三个配置项用于**控制和保护大模型调用的成本**，防止意外超支。具体含义如下：

---

### 1. **daily_budget**
- **含义**：每日总预算上限
- **作用**：当天所有模型调用累计花费超过这个金额后，系统会自动停止调用，防止一天内花费过多。
- **单位**：人民币（元）
- **默认值**：50.0（即每天最多花50元）

---

### 2. **per_chapter_limit**
- **含义**：每章节处理的预算上限
- **作用**：单个章节（或单次大任务）处理时，最多允许消耗的金额。超过后会中断该章节的处理，避免单次任务异常消耗大量费用。
- **单位**：人民币（元）
- **默认值**：2.0（即每章最多花2元）

---

### 3. **emergency_threshold**
- **含义**：紧急阈值
- **作用**：当剩余预算低于该阈值时，系统会触发警告或采取保护措施（如暂停新任务），防止账户余额耗尽。
- **单位**：人民币（元）
- **默认值**：0.1（即剩余不足1毛钱时报警）

---

**总结**：  
这三个参数共同保障了模型调用的**成本安全**，让你可以放心批量处理数据，不用担心意外高额账单。
# ParrotNovels Dev Container

这是 ParrotNovels 项目的 Dev Container 配置，提供了一个完整的、可重现的开发环境。

## 🚀 快速开始

### 前置要求

1. **Docker Desktop**: 确保已安装并运行
2. **VS Code**: 安装 `Remote - Containers` 扩展
3. **Git**: 用于克隆项目

### 启动开发环境

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd ParrotNovels
   ```

2. **在 VS Code 中打开**
   ```bash
   code .
   ```

3. **启动 Dev Container**
   - VS Code 会自动检测到 `.devcontainer` 配置
   - 点击弹出的 "Reopen in Container" 通知
   - 或者使用命令面板: `Dev Containers: Reopen in Container`

4. **等待初始化**
   - 首次启动会自动构建镜像并安装所有依赖
   - 查看终端输出了解进度

## 📋 环境配置

### 🐍 Python 环境
- **版本**: Python 3.12
- **包管理**: Conda + pip
- **虚拟环境**: `parrot`
- **路径**: `/opt/conda/envs/parrot/bin/python`

### 🌐 Node.js 环境
- **版本**: Node.js 22
- **包管理**: npm + Bun
- **全局包**: TypeScript, ESLint, Prettier

### 🗄️ 数据库
- **类型**: SQLite
- **位置**: `/workspace/data.db`
- **自动初始化**: 是

## 🌍 端口映射

| 服务 | 容器端口 | 主机端口 | 描述 |
|------|----------|----------|------|
| FastAPI 后端 | 8000 | 8000 | API 服务器 |
| Vite 前端 | 5173 | 5173 | 开发服务器 |
| LangGraph | 2024 | 2024 | 工作流开发 |
| NoVNC | 6080 | 6080 | 桌面环境 |
| VNC | 5901 | 5901 | VNC 连接 |

## 📁 文件挂载

### 绑定挂载 (实时同步)
- **源代码**: `../` → `/workspace`
- **资源文件**: `../resources` → `/workspace/resources`

### 数据卷 (性能优化)
- **Conda 缓存**: `conda-cache`
- **pip 缓存**: `pip-cache`
- **npm 缓存**: `npm-cache`
- **VS Code 服务器**: `vscode-server`

## 🔧 快速命令

### 启动服务

```bash
# 启动后端 (FastAPI)
bash /workspace/.devcontainer/start-backend.sh

# 启动前端 (Vite)
bash /workspace/.devcontainer/start-frontend.sh

# 启动 LangGraph 开发服务器
bash /workspace/.devcontainer/start-langgraph.sh

# 一键启动所有服务
bash /workspace/.devcontainer/start-all.sh
```

### 开发命令

```bash
# 激活 Python 环境
source /opt/conda/bin/activate parrot

# 运行后端测试
cd /workspace/backend
python -m pytest tests/

# 安装新的 Python 包
pip install <包名>

# 安装新的 Node.js 包
cd /workspace/frontend
pnpm add <包名>

# 代码格式化
black /workspace/backend/src/
prettier --write /workspace/frontend/src/
```

## ⚙️ 环境变量

### 主要配置文件
- **`.env`**: 主环境变量文件
- **`.devcontainer/.env.devcontainer`**: 容器专用配置

### 重要变量
```bash
# API 配置
API_HOST=0.0.0.0
API_PORT=8000

# 开发模式
DEBUG=true
FAST_STARTUP=true

# TTS 引擎
TTS_ENABLED_ENGINES=edge,index

# 数据库
DATABASE_FILE=/workspace/data.db
```

## 🎯 开发工作流

### 后端开发

1. **编辑代码**: `/workspace/backend/src/`
2. **运行测试**: `python -m pytest`
3. **启动服务**: `bash /workspace/start-backend.sh`
4. **API 文档**: http://localhost:8000/docs

### 前端开发

1. **编辑代码**: `/workspace/frontend/src/`
2. **运行开发服务器**: `bash /workspace/start-frontend.sh`
3. **访问应用**: http://localhost:5173
4. **热重载**: 自动检测文件变化

### 全栈开发

1. **启动所有服务**: `bash /workspace/start-all.sh`
2. **并行开发**: 前后端同时修改
3. **实时调试**: 使用 VS Code 调试器

## 🔍 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看 Docker 日志
docker logs parrot-novels-dev

# 重新构建容器
docker-compose -f .devcontainer/docker-compose.yml build --no-cache
```

#### 2. 端口冲突
```bash
# 检查端口占用
lsof -i :8000
lsof -i :5173

# 修改端口映射
# 编辑 .devcontainer/docker-compose.yml
```

#### 3. 依赖安装失败
```bash
# 重新运行初始化脚本
bash .devcontainer/setup.sh

# 手动安装依赖
pip install -r backend/requirements_llm.txt
cd frontend && pnpm install
```

#### 4. 权限问题
```bash
# 修复文件权限
sudo chown -R $(whoami) /workspace
chmod -R 755 /workspace
```

### 调试技巧

#### Python 调试
- 使用 VS Code 调试器
- 设置断点在 `.py` 文件中
- 配置 `launch.json` 用于 FastAPI

#### JavaScript 调试
- 使用浏览器开发者工具
- VS Code 的 JavaScript 调试器
- React DevTools 扩展

## 🛠️ 自定义配置

### 添加新的 Python 包

1. **编辑 requirements 文件**
   ```bash
   echo "new-package>=1.0.0" >> backend/requirements_llm.txt
   ```

2. **重新构建容器或手动安装**
   ```bash
   pip install new-package
   ```

### 添加新的 Node.js 包

1. **进入前端目录**
   ```bash
   cd /workspace/frontend
   ```

2. **安装包**
   ```bash
   pnpm add new-package
   ```

### 修改端口映射

编辑 `.devcontainer/docker-compose.yml`:

```yaml
ports:
  - "8000:8000"   # API 服务器
  - "5173:5173"   # 前端开发服务器
  - "3000:3000"   # 新端口映射
```

### 添加新的环境变量

编辑 `.devcontainer/.env.devcontainer`:

```bash
# 新的配置
NEW_SERVICE_PORT=3000
NEW_API_KEY=your_api_key
```

## 🔐 安全注意事项

1. **不要提交敏感信息**
   - API 密钥应设置为环境变量
   - 使用 `.env.local` 存储本地配置

2. **容器权限**
   - 开发环境使用 root 用户方便调试
   - 生产环境应使用非特权用户

3. **网络安全**
   - 开发容器绑定到所有接口 (0.0.0.0)
   - 仅在受信任的网络中使用

## 📚 扩展资源

### 文档链接
- [Dev Containers 官方文档](https://containers.dev/)
- [VS Code Remote 开发](https://code.visualstudio.com/docs/remote/remote-overview)
- [Docker Compose 参考](https://docs.docker.com/compose/)

### 项目相关
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [React 文档](https://react.dev/)
- [LangGraph 文档](https://langchain-ai.github.io/langgraph/)

### 开发工具
- [Tailwind CSS](https://tailwindcss.com/)
- [Zustand 状态管理](https://zustand-demo.pmnd.rs/)
- [Vite 构建工具](https://vitejs.dev/)

## 🤝 贡献指南

1. **分支管理**: 使用 feature 分支进行开发
2. **代码风格**: 遵循项目的 ESLint 和 Black 配置
3. **测试**: 确保添加适当的单元测试
4. **文档**: 更新相关文档和注释

## 📞 获取帮助

如果遇到问题或需要帮助：

1. **查看日志**: 检查容器和应用日志
2. **搜索文档**: 查阅相关技术文档
3. **提交 Issue**: 在项目仓库中创建问题
4. **讨论**: 参与项目讨论区 
# 版本控制
.git
.gitignore
.gitattributes

# 构建目录
node_modules/
frontend/node_modules/
frontend/dist/
frontend/build/

# Python 缓存和构建
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# 日志文件
*.log
resources/logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 音频和模型文件（太大，不应包含在镜像中）
resources/TTS/audio/
resources/TTS/index-tts/checkpoints/
resources/storage/

# 临时文件
*.tmp
*.temp
tmp/
temp/

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 环境变量文件
.env
.env.local
.env.*.local

# 测试覆盖率
coverage/
.coverage
.nyc_output

# 文档
docs/
*.md
README*

# 配置文件（开发容器会重新创建）
.devcontainer/ 
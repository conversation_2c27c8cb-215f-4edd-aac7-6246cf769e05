# 语句块合并功能

## 功能概述

为了提高TTS生成效率，在对话标注环节后新增了语句块合并功能。该功能能够自动合并连续的相同属性（type、speaker、emotion）语句块，减少TTS调用次数，提高整体处理效率。

## 实现方案

选择了**方案二：添加后处理节点**，理由如下：

### 为什么选择后处理节点而非提示词修改？

1. **标注质量保证**：当前的对话标注提示词已经很成熟，能够准确识别type、speaker、emotion。添加合并逻辑会增加任务复杂度，可能影响基础标注的准确性。

2. **可控性和稳定性**：后处理节点可以精确控制合并策略，合并结果更可预测。

3. **成本效益**：算法处理比LLM处理成本更低，且执行时间更短。

4. **便于调试优化**：可以单独测试和优化合并逻辑，不影响已经调试好的对话标注功能。

## 工作流变化

### 原工作流：
```
split_chapters → validate_input → analyze_characters → annotate_dialogues → quality_check
```

### 新工作流：
```
split_chapters → validate_input → analyze_characters → annotate_dialogues → merge_continuous_segments → quality_check
```

## 合并策略

### 合并条件
语句块被合并当且仅当满足以下**所有**条件：
- `type` 相同（dialogue、narration、monologue、unclassified）
- `speaker` 相同
- `emotion` 相同
- 在原始序列中连续

### 合并规则
- **对话内容**：直接连接，保持自然
- **叙述内容**：用空格连接
- **其他类型**：用空格连接

### 保留信息
- 原始语句块数量（`merged_count`字段）
- 合并统计信息（`merge_stats`字段）

## 实际效果示例

### 合并前（7个语句块）：
```json
[
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "第 1 章 - 节日家宴与拜访夫子"},
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "松儿的父亲，一位饱经风霜的皇甫氏后裔..."},
  {"type": "dialogue", "speaker": "父亲", "emotion": "sad", "content": "连年歉收，现下竟连五辛也凑不齐了..."},
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "他呷了一口酒，继续说道："},
  {"type": "dialogue", "speaker": "父亲", "emotion": "sad", "content": "唉！我皇甫一脉，原也算得是贵胄..."},
  {"type": "dialogue", "speaker": "父亲", "emotion": "sad", "content": "只可惜时移世异，陵迁谷变..."},
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "一旁的母亲，见父亲酒意渐浓..."}
]
```

### 合并后（5个语句块）：
```json
[
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "第 1 章 - 节日家宴与拜访夫子 松儿的父亲，一位饱经风霜的皇甫氏后裔...", "merged_count": 2},
  {"type": "dialogue", "speaker": "父亲", "emotion": "sad", "content": "连年歉收，现下竟连五辛也凑不齐了..."},
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "他呷了一口酒，继续说道："},
  {"type": "dialogue", "speaker": "父亲", "emotion": "sad", "content": "唉！我皇甫一脉，原也算得是贵胄...只可惜时移世异，陵迁谷变...", "merged_count": 2},
  {"type": "narration", "speaker": "旁白", "emotion": "neutral", "content": "一旁的母亲，见父亲酒意渐浓..."}
]
```

**压缩率：28.6%**（从7个块减少到5个块）

## 质量控制

### 新增质量指标
- `merge_efficiency`：平均压缩率，期望至少50%的压缩率

### 统计信息
每个合并后的章节包含以下统计信息：
```json
{
  "merge_stats": {
    "original_count": 25,     // 原始语句块数量
    "merged_count": 18,       // 合并后语句块数量  
    "reduction_rate": 0.28    // 压缩率
  }
}
```

## TTS效率提升

### 直接效益
- **减少TTS调用次数**：每次合并可减少一次TTS API调用
- **降低网络开销**：减少HTTP请求数量
- **加快处理速度**：减少I/O等待时间

### 预期效果

基于测试数据，预期可实现：

- 平均压缩率：20-40%
- TTS调用次数减少：20-40%
- 整体处理时间缩短：10-20%

## 使用方式

该功能已自动集成到小说处理工作流中，无需额外配置。处理结果在`merged_chapters`字段中可获取合并后的语句块。

## 调试支持

- 合并统计信息在`merge_stats`字段
- 可通过日志查看合并过程的详细信息 
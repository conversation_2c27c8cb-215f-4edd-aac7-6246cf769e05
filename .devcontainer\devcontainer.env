# Parrot Novels 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置值

# =============================================================================
# 应用配置
# =============================================================================

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=resources/logs/app.log

# 服务配置
API_HOST=127.0.0.1
API_PORT=8000
DEBUG=false

# 快速启动模式（跳过耗时的模型加载，方便调试）
FAST_STARTUP=true

# =============================================================================
# TTS 引擎配置
# =============================================================================

# 启用的TTS引擎列表（用逗号分隔）
# 可选值: edge, index, minimax
TTS_ENABLED_ENGINES=index,edge

# =============================================================================
# EdgeTTS 配置
# =============================================================================

# EdgeTTS 默认语音
EDGE_TTS_DEFAULT_VOICE=zh-CN-YunxiaNeural

# EdgeTTS 语速 (0.5-2.0)
EDGE_TTS_RATE=1.0

# EdgeTTS 音量 (0.0-1.0)
EDGE_TTS_VOLUME=1.0

# =============================================================================
# IndexTTS 配置
# =============================================================================

# IndexTTS 模型路径
INDEX_TTS_MODEL_PATH=resources/TTS/index-tts/checkpoints

# IndexTTS 配置文件路径
INDEX_TTS_CONFIG_PATH=resources/TTS/index-tts/checkpoints/config.yaml

# IndexTTS 精度 (fp16 或 fp32)
INDEX_TTS_PRECISION=fp16

# IndexTTS 运行设备 (cpu, cuda, auto)
# auto: 自动检测，有GPU用cuda，无GPU用cpu
INDEX_TTS_DEVICE=auto

# IndexTTS 是否使用CUDA内核 (true 或 false)
INDEX_TTS_USE_CUDA_KERNEL=true

# IndexTTS 默认参考语音文件路径
INDEX_TTS_REFERENCE_VOICE=resources/TTS/index-tts/reference_voices/zh-CN-XiaoxiaoNeural.wav

# =============================================================================
# MinimaxTTS 配置
# =============================================================================

# Minimax API Key
MINIMAX_API_KEY=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************.iulHGeAgLDoG3YYkrRXEFoZUa7v3TgPCDjR0Ega_6avkPvuQ8IIrdLH3O-HlikW-NirYosjdO26ULn-3u5ackAz2v1uy2R-yTBrAiFGq4bUD2pU74V6xuWWgQDPYEDVBwBXzxnuPcsKKT_mTG7cpAi6PKkQOwyjOLTxR8fgJrDQQZaSzYZ3XVR-mIJbkEHrYCAijF3pfnKQxuLA2etNyxAPK1Dhjy-tzZ63Dl4vypYG6Oc_RnPWAquMntF8m512maKupHGgbbaDBt2LuzzM5FqX9NKRgMoLtm-tx64luMeVbSi5O-cSRyy5nEgkfYf9c4bdANV4-YsmA36_b7lO46g

# Minimax API Base URL
MINIMAX_API_BASE=https://api.minimax.chat

# Minimax 默认语音模型
MINIMAX_DEFAULT_VOICE=speech-02-turbo

# =============================================================================
# 数据库配置
# =============================================================================

# SQLite 数据库文件路径
DATABASE_FILE=data.db

# =============================================================================
# AI 模型配置
# =============================================================================

OPENROUTER_API_KEY=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
DASHSCOPE_API_KEY=sk-0be87983404e442b9b73a4fea9c772ea
LLM_DAILY_BUDGET=50.0

# =============================================================================
# LangSmith 监控配置
# =============================================================================

# LangSmith API Key (从 https://smith.langchain.com 获取)
LANGSMITH_API_KEY="***************************************************"

# 是否启用 LangSmith 追踪 (true/false)
LANGSMITH_TRACING=true

# LangSmith 项目名称 (用于组织追踪数据)
LANGSMITH_PROJECT="parrot-novels-dev"

# LangSmith 端点 (通常使用默认值)
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
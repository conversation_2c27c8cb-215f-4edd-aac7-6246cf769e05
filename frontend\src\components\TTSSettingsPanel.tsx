import { RefreshCw, SlidersHorizontal, Cog } from 'lucide-react';
import { useTTSStore } from '../store/ttsStore';
import Slider from './Slider';

export default function TTSSettingsPanel() {
  const {
    settings,
    availableEngines,
    isLoadingEngines,
    engineError,
    updateSettings,
    getCurrentEngineModels,
    getCurrentEngine,
  } = useTTSStore();

  const currentEngineModels = getCurrentEngineModels();
  const currentEngine = getCurrentEngine();

  // 处理重试
  const handleRetry = async () => {
    if (window.__ttsRefetch) {
      await window.__ttsRefetch();
    }
  };

  // 检查当前引擎是否有特定参数（不包括基础语音参数）
  const hasEngineSpecificParams =
    currentEngine && Object.keys(currentEngine.parameters || {}).length > 0;

  return (
    <div className="panel animate-in">
      <div className="panel-header p-[16px] pt-[8px] pb-[8px]">
        <h2 className="panel-title text-sm flex items-center">
          <SlidersHorizontal size={12} className="mr-1" />
          TTS设置
        </h2>
        {currentEngine && (
          <div className="text-[10px] text-neutral-500">
            {currentEngine.description}
          </div>
        )}
      </div>

      <div className="panel-body space-y-6">
        {/* 错误提示 - 显示在顶部但不阻止界面 */}
        {engineError && (
          <div className="alert alert-warning">
            <div className="flex flex-col gap-2">
              <span className="text-sm">
                ⚠️ 后端服务连接失败，TTS功能暂不可用
              </span>
              <button
                className="btn btn-sm btn-ghost"
                onClick={handleRetry}
                disabled={isLoadingEngines}
              >
                {isLoadingEngines ? (
                  <>
                    <span className="loading loading-spinner loading-xs"></span>
                    重试中...
                  </>
                ) : (
                  <span className="flex items-center">
                    <RefreshCw size={14} className="mr-1" />
                    重试连接
                  </span>
                )}
              </button>
            </div>
          </div>
        )}

        {/* 加载状态 */}
        {isLoadingEngines && (
          <div className="flex items-center justify-center py-4">
            <div className="loading loading-spinner loading-md"></div>
            <span className="ml-2">加载引擎数据...</span>
          </div>
        )}

        {/* TTS引擎选择 */}
        <div>
          <label className="label">TTS引擎</label>
          <select
            value={settings.engine}
            onChange={(e) => {
              const newEngine = e.target.value;
              const engineData = availableEngines.find(
                (engine) => engine.id === newEngine
              );
              const firstModel = engineData?.models[0] || '';
              updateSettings({
                engine: newEngine,
                model: firstModel,
                parameters: {}, // 重置参数
              });
            }}
            className="select w-full p-[6px] text-xs"
            disabled={
              Boolean(engineError) ||
              isLoadingEngines ||
              availableEngines.length === 0
            }
          >
            {availableEngines.length === 0 ? (
              <option disabled>暂无可用引擎</option>
            ) : (
              availableEngines.map((engine) => (
                <option
                  key={engine.id}
                  value={engine.id}
                  disabled={!engine.is_available}
                >
                  {engine.name} {!engine.is_available && '(不可用)'}
                </option>
              ))
            )}
          </select>
          {currentEngine && (
            <div className="text-xs text-neutral-500 mt-1">
              支持语言: {currentEngine.supported_languages.join(', ')}
            </div>
          )}
        </div>

        {/* TTS模型选择 */}
        <div>
          <label className="label">TTS模型</label>
          <select
            value={settings.model}
            onChange={(e) => updateSettings({ model: e.target.value })}
            className="select w-full p-[6px] text-xs"
            disabled={
              Boolean(engineError) ||
              isLoadingEngines ||
              currentEngineModels.length === 0
            }
          >
            {currentEngineModels.length === 0 ? (
              <option disabled>暂无可用模型</option>
            ) : (
              currentEngineModels.map((model) => (
                <option key={model} value={model}>
                  {model}
                </option>
              ))
            )}
          </select>
        </div>

        {/* 引擎参数 */}
        {hasEngineSpecificParams && !engineError && (
          <div className="space-y-4">
            <div className="text-sm font-medium text-neutral-700 flex items-center gap-2">
              <Cog size={16} />
              <span>参数</span>
            </div>

            {Object.entries(currentEngine.parameters).map(
              ([paramName, paramConfig]) => {
                // 检查参数依赖关系
                if (paramConfig.depends_on) {
                  const dependencyMet = Object.entries(
                    paramConfig.depends_on
                  ).every(([depParam, depValue]) => {
                    const currentDepValue =
                      settings.parameters?.[depParam] ??
                      currentEngine.parameters[depParam]?.default;
                    return currentDepValue === depValue;
                  });

                  if (!dependencyMet) {
                    return null; // 依赖条件不满足，不显示此参数
                  }
                }

                const currentValue =
                  settings.parameters?.[paramName] ?? paramConfig.default;

                if (
                  paramConfig.type === 'bool' &&
                  paramConfig.control === 'switch'
                ) {
                  return (
                    <fieldset key={paramName} className="fieldset">
                      <label className="label cursor-pointer">
                        <span>{paramConfig.label}</span>
                        <input
                          type="checkbox"
                          className="toggle toggle-primary toggle-sm"
                          checked={Boolean(currentValue)}
                          onChange={(e) => {
                            updateSettings({
                              parameters: {
                                ...settings.parameters,
                                [paramName]: e.target.checked,
                              },
                            });
                          }}
                          disabled={isLoadingEngines}
                        />
                      </label>
                      <div className="text-xs text-neutral-500">
                        {paramConfig.description}
                      </div>
                    </fieldset>
                  );
                }

                if (
                  (paramConfig.type === 'int' ||
                    paramConfig.type === 'float') &&
                  paramConfig.control === 'slider'
                ) {
                  const [min, max] = paramConfig.range as number[];
                  return (
                    <div key={paramName}>
                      <Slider
                        label={paramConfig.label}
                        value={currentValue}
                        min={min}
                        max={max}
                        step={paramConfig.step || 1}
                        unit={paramConfig.unit || ''}
                        onChange={(value) =>
                          updateSettings({
                            parameters: {
                              ...settings.parameters,
                              [paramName]: value,
                            },
                          })
                        }
                      />

                      <div className="text-xs text-neutral-500 mt-1">
                        {paramConfig.description}
                      </div>
                    </div>
                  );
                }

                return null;
              }
            )}
          </div>
        )}
      </div>
    </div>
  );
}

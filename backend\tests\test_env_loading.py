#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境变量加载顺序
"""

import os
import sys
from pathlib import Path

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))


def test_env_loading():
    """测试环境变量加载"""
    print("=== 环境变量加载测试 ===")

    # 1. 检查初始环境变量
    print(f"1. 初始 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")

    # 2. 手动加载 .env 文件
    from dotenv import load_dotenv

    env_file = Path(".env")
    if env_file.exists():
        print(f"2. 发现 .env 文件: {env_file.absolute()}")
        load_dotenv(env_file)
        print(f"   加载后 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")
    else:
        print("2. .env 文件不存在")

    # 3. 导入和初始化系统
    print("3. 开始系统初始化...")
    from core.system_init import initialize_system

    success = initialize_system()
    print(f"   系统初始化结果: {success}")
    print(f"   初始化后 INDEX_TTS_MODEL_PATH = {os.getenv('INDEX_TTS_MODEL_PATH')}")

    # 4. 测试 IndexTTS 配置
    print("4. 测试 IndexTTS 配置加载...")
    from tts.index.tts_impl import _get_env_config

    config = _get_env_config()
    print(f"   IndexTTS 配置: {config}")

    # 5. 创建 IndexTTS 实例
    print("5. 创建 IndexTTS 实例...")
    try:
        from tts.index.tts_impl import IndexTTS

        tts = IndexTTS()
        print(f"   IndexTTS 模型路径: {tts.model_dir}")
        print(f"   IndexTTS 配置路径: {tts.cfg_path}")
    except Exception as e:
        print(f"   创建 IndexTTS 失败: {e}")


if __name__ == "__main__":
    test_env_loading()

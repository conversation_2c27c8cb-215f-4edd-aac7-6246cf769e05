#!/bin/bash

echo "🚀 启动 ParrotNovels 完整开发环境..."

# 启动后端
echo "📡 启动后端服务 (端口 8000)..."
bash /workspace/start-backend.sh &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端
echo "🌐 启动前端服务 (端口 5173)..."
bash /workspace/start-frontend.sh &
FRONTEND_PID=$!

# 启动 LangGraph (可选)
if [ -f "/workspace/langgraph.json" ]; then
    echo "🔗 启动 LangGraph 开发服务器 (端口 2024)..."
    bash /workspace/start-langgraph.sh &
    LANGGRAPH_PID=$!
fi

echo "✅ 所有服务已启动!"
echo "📡 后端 API: http://localhost:8000"
echo "🌐 前端界面: http://localhost:5173"
if [ ! -z "$LANGGRAPH_PID" ]; then
    echo "🔗 LangGraph: http://localhost:2024"
fi

# 等待任意服务退出
wait

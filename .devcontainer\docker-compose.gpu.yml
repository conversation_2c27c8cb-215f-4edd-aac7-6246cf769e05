version: '3.8'

# GPU 支持的 Dev Container 配置
# 使用方法: 在 devcontainer.json 中将 dockerComposeFile 改为 "docker-compose.gpu.yml"

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile.gpu
      args:
        # 构建参数
        PYTHON_VERSION: "3.12"
        NODE_VERSION: "22"
        CONDA_VERSION: "latest"
        CUDA_VERSION: "12.1"
    
    # 容器名称
    container_name: parrot-novels-dev-gpu
    
    # 工作目录
    working_dir: /workspace
    
    # 挂载配置
    volumes:
      # 源代码挂载（绑定挂载，用于实时开发）
      - ../:/workspace:cached
      
      # 前端 node_modules 专用挂载（避免跨平台兼容性问题）
      - frontend-node-modules-gpu:/workspace/frontend/node_modules
      
      # 缓存挂载（命名卷，提高性能）
      - conda-cache-gpu:/opt/conda/pkgs
      - pip-cache-gpu:/root/.cache/pip
      - npm-cache-gpu:/root/.npm
      - yarn-cache-gpu:/root/.cache/yarn
      
      # 模型和资源文件（绑定挂载，但使用缓存一致性）
      - ../resources:/workspace/resources:cached
      
      # Git 配置挂载（可选，保持 Git 配置）
      - ~/.gitconfig:/root/.gitconfig:ro
      - ~/.ssh:/root/.ssh:ro
      
      # VS Code 服务器数据持久化
      - vscode-server-gpu:/root/.vscode-server
      - vscode-server-insiders-gpu:/root/.vscode-server-insiders
    
    # 端口映射
    ports:
      - "8000:8000"   # FastAPI 后端
      - "5173:5173"   # Vite 前端开发服务器
      - "2024:2024"   # LangGraph 开发服务器
      - "6080:6080"   # 桌面环境 (NoVNC)
      - "5901:5901"   # VNC 端口
      - "8888:8888"   # Jupyter Notebook (GPU 开发)
    
    # 环境变量文件
    env_file:
      - devcontainer.env
    
    # GPU 专用环境变量（覆盖env_file中的配置）
    environment:
      # GPU 相关环境变量
      - CUDA_VISIBLE_DEVICES=all
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      
      # TTS 配置（启用 GPU 加速）
      - INDEX_TTS_DEVICE=cuda
      - INDEX_TTS_USE_CUDA_KERNEL=true
      - TTS_ENABLED_ENGINES=edge,index,minimax
      
      # 显示相关（用于可能的 GUI 应用）
      - DISPLAY=:1
      - VNC_RESOLUTION=1920x1080x24
    
    # GPU 支持配置
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    
    # 运行时配置（用于 Docker Compose V2）
    runtime: nvidia
    
    # 网络配置
    networks:
      - parrot-network-gpu
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 重启策略
    restart: unless-stopped
    
    # 保持容器运行
    tty: true
    stdin_open: true
    
    # 安全配置
    cap_add:
      - SYS_PTRACE  # 用于调试
    
    # 共享内存大小（用于音频处理和 GPU 计算）
    shm_size: 2g

# 网络配置
networks:
  parrot-network-gpu:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  # GPU 环境专用缓存卷
  conda-cache-gpu:
    driver: local
  pip-cache-gpu:
    driver: local
  npm-cache-gpu:
    driver: local
  yarn-cache-gpu:
    driver: local
  
  # 前端 node_modules 专用卷
  frontend-node-modules-gpu:
    driver: local
  
  # VS Code 服务器数据
  vscode-server-gpu:
    driver: local
  vscode-server-insiders-gpu:
    driver: local 
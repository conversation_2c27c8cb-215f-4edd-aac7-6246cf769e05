import type { Novel, APIResponse } from '../types';

// API 基础配置
const API_BASE_URL = 'http://localhost:8000';

// 通用 API 请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<APIResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.detail || '请求失败',
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络错误',
    };
  }
}

// 小说相关 API
export const novelApi = {
  // 获取所有小说
  getAll: () => apiRequest<Novel[]>('/api/novels'),

  // 获取单个小说
  getById: (id: string) => apiRequest<Novel>(`/api/novels/${id}`),

  // 创建小说
  create: (novel: Omit<Novel, 'id' | 'createdAt' | 'updatedAt'>) =>
    apiRequest<Novel>('/api/novels', {
      method: 'POST',
      body: JSON.stringify(novel),
    }),

  // 更新小说
  update: (id: string, updates: Partial<Novel>) =>
    apiRequest<Novel>(`/api/novels/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    }),

  // 删除小说
  delete: (id: string) =>
    apiRequest<void>(`/api/novels/${id}`, {
      method: 'DELETE',
    }),

  // 上传文件
  uploadFile: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    return apiRequest<Novel>('/api/novels/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // 清空 Content-Type，让浏览器自动设置
    });
  },
};

// TTS 相关 API
export const ttsApi = {
  // 生成语音
  generateAudio: (text: string, voice: string) =>
    apiRequest<{ audioUrl: string }>('/api/tts/generate', {
      method: 'POST',
      body: JSON.stringify({ text, voice }),
    }),

  // 获取可用声音列表
  getVoices: () => apiRequest<string[]>('/api/tts/voices'),

  // 获取声音文件URL（用于试听）
  getVoiceFileUrl: (voicePath: string) => {
    return `${API_BASE_URL}/api/voices/${voicePath}`;
  },

  // 获取声音文件Blob（用于音频播放）
  getVoiceFile: async (voicePath: string): Promise<Blob | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/voices/${voicePath}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.blob();
    } catch (error) {
      console.error('获取声音文件失败:', error);
      return null;
    }
  },
};

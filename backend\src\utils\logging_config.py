#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目统一日志配置模块
提供整个项目的日志配置功能
"""

import logging
import sys
from pathlib import Path
from typing import Union


def setup_logging(
    level: str = "INFO",
    format_str: str = None,
    log_file: Union[str, Path] = None,
    console_output: bool = True,
):
    """配置项目的日志系统

    Args:
        level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        format_str: 自定义日志格式
        log_file: 日志文件路径（可选），支持相对路径和绝对路径
        console_output: 是否输出到控制台
    """

    # 默认日志格式
    if format_str is None:
        format_str = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

    # 设置日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)

    # 使用 logging.basicConfig 替代手动管理处理器，并添加 force=True
    logging.basicConfig(
        level=log_level,
        format=format_str,
        force=True,  # 关键：移除并替换现有配置
    )

    root_logger = logging.getLogger()

    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(logging.Formatter(format_str))
        root_logger.addHandler(console_handler)

    # 文件处理器（如果指定了日志文件）
    if log_file:
        try:
            # 尝试使用path_utils处理路径
            from .path_utils import ensure_dir_exists, resolve_path

            # 解析路径
            log_path = (
                resolve_path(log_file)
                if not Path(log_file).is_absolute()
                else Path(log_file)
            )

            # 确保日志目录存在
            ensure_dir_exists(log_path.parent)

        except ImportError:
            # 如果path_utils不可用，使用基本路径处理
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_path, encoding="utf-8")
        file_handler.setLevel(log_level)
        file_handler.setFormatter(logging.Formatter(format_str))
        root_logger.addHandler(file_handler)


def setup_app_logging(level: str = "INFO", quiet: bool = False):
    """为应用程序配置简化的日志

    Args:
        level: 日志级别
        quiet: 静默模式，不输出到控制台
    """
    # 直接调用 logging.basicConfig
    log_level = "ERROR" if quiet else level.upper()
    format_str = "%(levelname)s - %(name)s - %(message)s"

    logging.basicConfig(level=log_level, format=format_str, force=True)


def setup_tts_logging(level: str = "INFO", quiet: bool = False):
    """为TTS模块配置日志（向后兼容）

    Args:
        level: 日志级别
        quiet: 静默模式，不输出到控制台
    """
    setup_app_logging(level=level, quiet=quiet)


def get_logger(name: str) -> logging.Logger:
    """获取项目记录器

    Args:
        name: 记录器名称，通常使用 __name__

    Returns:
        配置好的记录器实例
    """
    return logging.getLogger(name)


# 向后兼容的别名
get_tts_logger = get_logger

---
description: Tailwind CSS and UI component best practices for modern web applications
globs: **/*.css, **/*.tsx, **/*.jsx
alwaysApply: false
---

# Tailwind CSS 最佳实践

## 项目设置
- 使用正确的 Tailwind 配置
- 正确配置主题扩展
- 设置正确的 purge 配置
- 使用正确的插件集成
- 配置自定义间距和断点
- 设置正确的调色板

## 组件样式
- 使用工具类而非自定义 CSS
- 需要时使用 @apply 分组相关工具类
- 使用正确的响应式设计工具类
- 正确实现深色模式
- 使用正确的状态变体
- 保持组件样式一致

## 布局
- 有效使用 Flexbox 和 Grid 工具类
- 实施正确的间距系统
- 需要时使用容器查询
- 实施正确的响应式断点
- 使用正确的内边距和外边距工具类
- 实施正确的对齐工具类

## 排版
- 使用正确的字体大小工具类
- 实施正确的行高
- 使用正确的字重工具类
- 正确配置自定义字体
- 使用正确的文本对齐
- 实施正确的文本装饰

## 颜色
- 使用语义化颜色命名
- 实施正确的颜色对比度
- 有效使用不透明度工具类
- 正确配置自定义颜色
- 使用正确的渐变工具类
- 实施正确的悬停状态

## 组件
- 可用时使用 shadcn/ui 组件
- 正确扩展组件
- 保持组件变体一致
- 实施正确的动画
- 使用正确的过渡工具类
- 牢记无障碍性

## 响应式设计
- 使用移动优先方法
- 实施正确的断点
- 有效使用容器查询
- 正确处理不同屏幕尺寸
- 实施正确的响应式排版
- 使用正确的响应式间距

## 性能
- 使用正确的 purge 配置
- 最小化自定义 CSS
- 使用正确的缓存策略
- 实施正确的代码分割
- 针对生产环境进行优化
- 监控打包大小

## 最佳实践
- 遵循命名约定
- 保持样式井然有序
- 使用正确的文档
- 实施正确的测试
- 遵循无障碍指南
- 使用正确的版本控制
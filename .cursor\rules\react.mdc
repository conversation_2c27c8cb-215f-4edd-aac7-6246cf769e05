---
description: React best practices and patterns for modern web applications
globs: **/*.tsx, **/*.jsx,frontend/src/components/**/*
alwaysApply: false
---

# React 最佳实践

## 组件结构
- 使用函数组件而非类组件
- 保持组件小而专注
- 将可复用逻辑提取到自定义 Hook
- 使用组合而非继承
- 使用 TypeScript 实现正确的 prop 类型
- 将大型组件拆分为更小、更专注的组件

## Hooks
- 遵循 Hook 规则
- 使用自定义 Hook 实现可复用逻辑
- 保持 Hook 专注和简单
- 在 useEffect 中使用适当的依赖数组
- 需要时在 useEffect 中实现清理操作
- 避免嵌套 Hook

## 状态管理
- 使用 useState 管理局部组件状态
- 对复杂状态逻辑使用 useReducer
- 使用 Context API 管理共享状态
- 尽可能将状态保持在离使用它的地方最近
- 通过合理的状态管理避免 prop drilling
- 仅在必要时使用状态管理库

## 性能
- 实施正确的记忆化 (useMemo, useCallback)
- 对昂贵的组件使用 React.memo
- 避免不必要的重新渲染
- 实施正确的懒加载
- 在列表中使用正确的 key prop
- 分析和优化渲染性能

## 表单
- 对表单输入使用受控组件
- 实施正确的表单校验
- 正确处理表单提交状态
- 显示适当的加载和错误状态
- 对复杂表单使用表单库
- 为表单实施正确的无障碍性

## 错误处理
- 实施错误边界 (Error Boundaries)
- 正确处理异步错误
- 显示用户友好的错误信息
- 实施正确的回退 UI
- 适当地记录错误
- 优雅地处理边界情况

## 测试
- 为组件编写单元测试
- 为复杂流程实施集成测试
- 使用 React Testing Library
- 测试用户交互
- 测试错误场景
- 实施正确的模拟数据

## 无障碍性
- 使用语义化 HTML 元素
- 实施正确的 ARIA 属性
- 确保键盘导航
- 使用屏幕阅读器进行测试
- 处理焦点管理
- 为图片提供正确的 alt 文本

## 代码组织
- 将相关组件组织在一起
- 使用合理的文件命名约定
- 实施正确的目录结构
- 将样式保持在组件附近
- 使用正确的导入/导出
- 记录复杂组件逻辑
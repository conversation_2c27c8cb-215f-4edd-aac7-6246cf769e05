"""
LangSmith 监控配置模块
"""

import os
from functools import wraps
from typing import Optional


class LangSmithConfig:
    """LangSmith 配置管理"""

    @staticmethod
    def is_enabled() -> bool:
        """检查是否启用 LangSmith 追踪"""
        return os.getenv("LANGSMITH_TRACING", "false").lower() == "true"

    @staticmethod
    def get_api_key() -> Optional[str]:
        """获取 LangSmith API Key"""
        return os.getenv("LANGSMITH_API_KEY")

    @staticmethod
    def get_project_name() -> str:
        """获取项目名称"""
        return os.getenv("LANGSMITH_PROJECT", "parrot-novels-dev")

    @staticmethod
    def get_endpoint() -> str:
        """获取端点"""
        return os.getenv("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com")

    @classmethod
    def setup_tracing(cls) -> bool:
        """设置 LangSmith 追踪"""
        if not cls.is_enabled():
            print("LangSmith 追踪未启用")
            return False

        api_key = cls.get_api_key()
        if not api_key or api_key == "your_langsmith_api_key":
            print("警告: LangSmith API Key 未配置或使用默认值")
            return False

        # 设置环境变量（使用新版 LANGSMITH_ 前缀）
        os.environ["LANGSMITH_API_KEY"] = api_key
        os.environ["LANGSMITH_TRACING"] = "true"
        os.environ["LANGSMITH_PROJECT"] = cls.get_project_name()
        os.environ["LANGSMITH_ENDPOINT"] = cls.get_endpoint()

        # 同时设置 LangChain 内部使用的环境变量（兼容 langsmith 库）
        os.environ["LANGCHAIN_API_KEY"] = api_key
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGCHAIN_PROJECT"] = cls.get_project_name()
        os.environ["LANGCHAIN_ENDPOINT"] = cls.get_endpoint()

        print(f"LangSmith 追踪已启用，项目: {cls.get_project_name()}")
        return True

    @classmethod
    def create_run_metadata(cls, workflow_id: str, novel_title: str) -> dict:
        """创建运行元数据"""
        return {
            "workflow_id": workflow_id,
            "novel_title": novel_title,
            "project": cls.get_project_name(),
            "component": "novel_processing_workflow",
        }


def with_langsmith_tracing(
    run_name: str = None, tags: list = None, exclude_fields: list = None
):
    """
    LangSmith 追踪装饰器

    Args:
        run_name: 运行名称
        tags: 标签列表
        exclude_fields: 从输入输出中排除的字段列表
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not LangSmithConfig.is_enabled():
                return func(*args, **kwargs)

            try:
                from langsmith import traceable

                # 动态设置运行名称
                actual_run_name = run_name or f"{func.__name__}"
                actual_tags = tags or ["novel_processing"]

                # 如果指定了排除字段，创建一个过滤函数
                if exclude_fields:
                    excluded_fields_set = set(exclude_fields)

                    def filter_state(obj):
                        """过滤状态对象，排除指定字段"""
                        if isinstance(obj, dict):
                            return {
                                key: value
                                for key, value in obj.items()
                                if key not in excluded_fields_set
                            }
                        return obj

                    # 过滤输入参数
                    filtered_args = tuple(
                        filter_state(arg) if isinstance(arg, dict) else arg
                        for arg in args
                    )
                    filtered_kwargs = {
                        k: filter_state(v) if isinstance(v, dict) else v
                        for k, v in kwargs.items()
                    }

                    # 创建一个包装函数来过滤输出
                    def filtered_func(*f_args, **f_kwargs):
                        result = func(*f_args, **f_kwargs)
                        return (
                            filter_state(result) if isinstance(result, dict) else result
                        )

                    # 应用 traceable 装饰器到过滤后的函数
                    traced_func = traceable(
                        run_type="chain", name=actual_run_name, tags=actual_tags
                    )(filtered_func)

                    # 执行，但返回原始结果（不是过滤后的）
                    traced_func(*filtered_args, **filtered_kwargs)
                    return func(*args, **kwargs)
                else:
                    # 没有排除字段，使用原始的追踪方式
                    traced_func = traceable(
                        run_type="chain", name=actual_run_name, tags=actual_tags
                    )(func)

                    return traced_func(*args, **kwargs)

            except ImportError:
                print("警告: langsmith 未安装，跳过追踪")
                return func(*args, **kwargs)
            except Exception as e:
                print(f"LangSmith 追踪错误: {e}")
                return func(*args, **kwargs)

        return wrapper

    return decorator


def log_workflow_metrics(
    state: dict, step_name: str, metrics: dict = None, exclude_fields: list = None
):
    """
    记录工作流指标到 LangSmith

    Args:
        state: 工作流状态字典
        step_name: 步骤名称
        metrics: 自定义指标字典
        exclude_fields: 需要排除的字段列表，默认排除大字段内容
    """
    if not LangSmithConfig.is_enabled():
        return

    try:
        from langsmith import Client

        client = Client()

        # 默认排除的大字段（如果没有指定exclude_fields）
        default_excluded_fields = [
            "novel_content",  # 小说原始内容
            "chapter_contents",  # 章节内容列表
        ]

        # 使用传入的排除列表，如果没有传入则使用默认值
        if exclude_fields is None:
            exclude_fields = default_excluded_fields

        # 转换为集合以提高查找效率
        excluded_fields_set = set(exclude_fields)

        # 创建过滤后的状态副本
        filtered_state = {
            key: value for key, value in state.items() if key not in excluded_fields_set
        }

        # 基本指标
        base_metrics = {
            "step": step_name,
            "workflow_id": filtered_state.get("workflow_id"),
            "processing_progress": filtered_state.get("processing_progress", 0),
            "total_chapters": filtered_state.get("total_chapters", 0),
            "current_chapter": filtered_state.get("current_chapter", 0),
            "retry_count": filtered_state.get("retry_count", 0),
        }

        # 为被排除的字段添加统计信息（仅长度，不包含内容预览）
        if "novel_content" in excluded_fields_set and "novel_content" in state:
            base_metrics["novel_content_length"] = len(state["novel_content"])
            # 注意：不添加内容预览以完全避免在 LangSmith 中显示小说内容

        if (
            "chapter_contents" in excluded_fields_set
            and "chapter_contents" in state
            and isinstance(state["chapter_contents"], list)
        ):
            base_metrics["chapter_contents_count"] = len(state["chapter_contents"])
            if state["chapter_contents"]:
                base_metrics["avg_chapter_length"] = sum(
                    len(str(chapter)) for chapter in state["chapter_contents"]
                ) // len(state["chapter_contents"])
                # 添加每章长度统计
                base_metrics["chapter_lengths"] = [
                    len(str(chapter)) for chapter in state["chapter_contents"]
                ]

        # 合并自定义指标
        if metrics:
            base_metrics.update(metrics)

        # 添加其他有用的状态信息（从过滤后的状态中获取）
        state_summary = {
            "novel_title": filtered_state.get("novel_title"),
            "status": filtered_state.get("status"),
            "error": filtered_state.get("error"),
            "characters_count": len(filtered_state.get("characters", [])),
            "annotated_chapters_count": len(
                filtered_state.get("annotated_chapters", [])
            ),
            "chapters_with_merge_stats": sum(
                1
                for chapter in filtered_state.get("annotated_chapters", [])
                if chapter.get("merge_stats")
            ),
            "quality_scores": filtered_state.get("quality_scores", {}),
        }

        # 最终合并所有指标
        final_metrics = {**base_metrics, **state_summary}

        # 记录排除的字段信息用于调试
        if exclude_fields:
            final_metrics["_excluded_fields"] = exclude_fields
            final_metrics["_excluded_count"] = len(exclude_fields)

        # 记录指标（注意：这需要在运行上下文中）
        excluded_info = (
            f" [排除字段: {exclude_fields}]" if exclude_fields else " [无排除字段]"
        )
        # print(f"LangSmith 指标记录: {step_name}{excluded_info} - {final_metrics}")

    except ImportError:
        pass  # langsmith 未安装
    except Exception as e:
        print(f"LangSmith 指标记录错误: {e}")

import asyncio
from typing import Any, Dict, Iterator, Optional

import edge_tts

from ..engine import TTSEngineBase

try:
    from src.schemas.tts_schema import TTSVoice
except ImportError:
    pass


class EdgeTTS(TTSEngineBase):
    """Edge TTS实现"""

    def initialize(self) -> bool:
        return True

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.voice = self.config.get("voice", "zh-CN-XiaoxiaoNeural")
        self.rate = self.config.get("rate", "+0%")
        self.volume = self.config.get("volume", "+0%")
        self.pitch = self.config.get("pitch", "+0Hz")

    def synthesize(
        self,
        text: str,
        voice_id_name: str,
        voice_style_name: str,
        output_path: Optional[str] = None,
        **kwargs,
    ) -> bytes:
        """合成语音"""
        voice = voice_id_name or kwargs.get("voice", self.voice)
        # 注意：EdgeTTS 可能需要根据voice_style_name调整voice参数，这里暂时保持原有逻辑
        rate = kwargs.get("rate", self.rate)
        volume = kwargs.get("volume", self.volume)
        pitch = kwargs.get("pitch", self.pitch)

        async def _synthesize():
            communicate = edge_tts.Communicate(
                text, voice, rate=rate, volume=volume, pitch=pitch
            )
            audio_data_tmp = b""
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data_tmp += chunk["data"]
            return audio_data_tmp

        audio_data = asyncio.run(_synthesize())

        if output_path:
            self.save_audio(audio_data, output_path)

        return audio_data

    def synthesize_stream(
        self, text: str, voice_id_name: str, voice_style_name: str, **kwargs
    ) -> Iterator[bytes]:
        """流式合成语音"""
        voice = voice_id_name or kwargs.get("voice", self.voice)
        # 注意：EdgeTTS 可能需要根据voice_style_name调整voice参数，这里暂时保持原有逻辑
        rate = kwargs.get("rate", self.rate)
        volume = kwargs.get("volume", self.volume)
        pitch = kwargs.get("pitch", self.pitch)

        async def _stream():
            communicate = edge_tts.Communicate(
                text, voice, rate=rate, volume=volume, pitch=pitch
            )
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    yield chunk["data"]

        async def _run_stream():
            chunks = []
            async for chunk in _stream():
                chunks.append(chunk)
            return chunks

        chunks = asyncio.run(_run_stream())
        for chunk in chunks:
            yield chunk

    def _format_voice_display_name(self, voice: Dict[str, Any]) -> str:
        """格式化语音显示名称

        Args:
            voice: 语音信息字典

        Returns:
            格式化后的显示名称，如: YunxiaNeural（女）-中文-中国大陆
        """
        # 从ShortName中提取语音名称，如 zh-CN-XiaoxiaoNeural -> XiaoxiaoNeural
        voice_name = voice["ShortName"].split("-")[-1]

        # 性别映射
        gender_map = {"Male": "男", "Female": "女"}
        gender_text = gender_map.get(voice["Gender"], voice["Gender"])

        # 地区映射
        locale_map = {
            "zh-CN": "中文-大陆",
            "zh-CN-shaanxi": "中文-陕西",
            "zh-CN-liaoning": "中文-辽宁",
            "zh-HK": "中文-香港",
            "zh-TW": "中文-台湾",
            "en-GB": "英语-英国",
            "en-US": "英语-美国",
        }

        locale = voice["Locale"]
        locale_text = locale_map.get(locale, locale)

        return f"{voice_name}（{gender_text}）-{locale_text}"

    def get_configurable_parameters(self) -> Dict[str, Dict[str, Any]]:
        """获取Edge TTS可配置的参数信息

        Returns:
            参数配置字典，包含语音参数
        """
        return {
            "speed": {
                "type": "float",
                "control": "slider",
                "label": "语速",
                "range": [0.5, 2.0],
                "default": 1.0,
                "description": "语速调节，1.0为正常速度",
                "unit": "x",
                "step": 0.1,
                "category": "voice",
                "advanced": False,
            },
            "pitch": {
                "type": "float",
                "control": "slider",
                "label": "音调",
                "range": [0.5, 2.0],
                "default": 1.0,
                "description": "音调调节，1.0为正常音调",
                "unit": "x",
                "step": 0.1,
                "category": "voice",
                "advanced": False,
            },
            "volume": {
                "type": "int",
                "control": "slider",
                "label": "音量",
                "range": [0, 100],
                "default": 80,
                "description": "音量调节，100为最大音量",
                "unit": "%",
                "step": 1,
                "category": "voice",
                "advanced": False,
            },
        }

    def get_engine_info(self) -> Dict[str, Any]:
        """获取引擎信息

        Returns:
            引擎信息字典
        """
        return {
            "name": "EdgeTTS",
            "version": "7.0.2",
            "description": "Microsoft Edge浏览器内置的语音合成引擎",
            "models": ["默认"],
            "type": "cloud",
            "supported_languages": [
                "中",
                "英",
            ],
            "supports_streaming": True,
        }

# Node
**/node_modules/
**/dist/
**/build/
**/.cache/
**/pnpm-lock.yaml


# Editor and IDE Settings
**/*.code-workspace
**/*.swp
**/*.swo
**/.idea/

# OS-specific
**/Thumbs.db
**/ehthumbs.db
**/.Trashes
**/.DS_Store

# Logs
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/pnpm-debug.log*
**/lerna-debug.log*
**/*.log

# Environment Variables and Configs
# .env
# .env.*
!.env.example
!.env.test

# npm / Yarn / pnpm Lock Files (if using one exclusively)
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml


# git
.git
.github
.cursorignore
.standard-firmware
**/temp

# 数据库文件
**/*.db
**/*.sqlite

# Python
**/.venv/
**/__pycache__
*.pyc
*.pyo
*.pyd
.Python
.ipynb_checkpoints/
**/*.ipynb

# .dev
.dev/
uv.lock

# Testing
# *.test.*
# *.spec.*

# Large media fles
*.mp4
*.mov
*.wav
*.mp3
*.pdf
*.zip
*.tar.gz
*.pth
*.model
*.vocab


**/.pytest_cache/
**/.coverage

# LangGraph
*.pckl

.serena/
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本API集成测试
"""

import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)


class TestTextAPI:
    """测试文本API"""

    def test_format_novel_content_success(self):
        """测试成功格式化小说内容"""
        request_data = {
            "content": """第一章 开始

这是一个测试小说。这个段落很长，需要进行自动换行处理。

第二章 继续

这是第二章的内容。""",
            "max_line_width": 60,
            "enable_paragraph_indent": True,
            "indent_spaces": 4,
            "enable_chapter_formatting": True,
        }

        response = client.post("/api/text/format", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["formatted_content"] is not None
        assert data["chapter_count"] == 2
        assert data["original_length"] > 0
        assert data["formatted_length"] > 0
        assert data["processing_time"] > 0

    def test_format_novel_content_empty(self):
        """测试空内容格式化"""
        request_data = {
            "content": "",
        }

        response = client.post("/api/text/format", json=request_data)

        assert response.status_code == 400
        assert "文本内容不能为空" in response.json()["detail"]

    def test_format_novel_content_custom_pattern(self):
        """测试自定义章节模式"""
        request_data = {
            "content": """Chapter One: Beginning

This is chapter one content.

Chapter Two: Adventure

This is chapter two content.""",
            "enable_chapter_formatting": True,
            "chapter_pattern": r"^Chapter\s+\w+:\s+.*$",
        }

        response = client.post("/api/text/format", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["chapter_count"] == 2

    def test_clean_text_content_success(self):
        """测试成功清理文本内容"""
        request_data = {
            "content": "\ufeff  测试内容  \r\n\r\n  另一行内容  \r",
            "remove_bom": True,
            "normalize_line_breaks": True,
            "remove_extra_whitespace": True,
            "remove_empty_lines": True,
        }

        response = client.post("/api/text/clean", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["cleaned_content"] is not None
        assert data["original_length"] > 0
        assert data["cleaned_length"] > 0
        assert data["processing_time"] > 0

    def test_clean_text_content_empty(self):
        """测试空内容清理"""
        request_data = {
            "content": "",
        }

        response = client.post("/api/text/clean", json=request_data)

        assert response.status_code == 400
        assert "文本内容不能为空" in response.json()["detail"]

    def test_format_help_endpoint(self):
        """测试帮助信息端点"""
        response = client.get("/api/text/format/help")

        assert response.status_code == 200
        data = response.json()
        assert "title" in data
        assert "endpoints" in data
        assert "chapter_patterns" in data
        assert "parameters" in data

        # 检查端点信息
        assert "/text/format" in data["endpoints"]
        assert "/text/clean" in data["endpoints"]

        # 检查参数信息
        assert "max_line_width" in data["parameters"]
        assert "enable_chapter_formatting" in data["parameters"]

    def test_format_with_invalid_parameters(self):
        """测试无效参数"""
        request_data = {
            "content": "测试内容",
            "max_line_width": 10,  # 太小
        }

        response = client.post("/api/text/format", json=request_data)

        # 应该返回参数验证错误
        assert response.status_code == 422

    def test_format_with_long_content(self):
        """测试长内容格式化"""
        long_content = (
            "这是一个很长的测试内容。" * 1000
            + "\n\n第一章 测试\n\n"
            + "更多内容。" * 500
        )

        request_data = {
            "content": long_content,
            "max_line_width": 80,
        }

        response = client.post("/api/text/format", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["chapter_count"] >= 1


if __name__ == "__main__":
    pytest.main([__file__])

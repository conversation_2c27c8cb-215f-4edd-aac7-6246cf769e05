{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.12.3", "engines": {"node": "22.16.0", "pnpm": ">=10.0.0"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "preview": "vite preview"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@tanstack/react-query": "^5.77.2", "@types/wavesurfer.js": "^6.0.12", "daisyui": "^5.0.40", "diff": "^6.0.0", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-json-view-lite": "^2.4.1", "react-resizable-panels": "^3.0.3", "react-router-dom": "^7.6.1", "sonner": "^2.0.3", "wavesurfer.js": "^7.9.5", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "volta": {"node": "22.16.0"}}
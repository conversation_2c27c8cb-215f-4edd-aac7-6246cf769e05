import { useState, useEffect, useCallback } from 'react';
import type { TTSParametersConfig, TTSParameterValues } from '../types/tts';
import { ttsApi } from '../services/ttsApi';

interface UseTTSParametersReturn {
  /** 参数配置 */
  parametersConfig: TTSParametersConfig;
  /** 当前参数值 */
  parameterValues: TTSParameterValues;
  /** 是否显示高级选项 */
  showAdvanced: boolean;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 设置参数值 */
  setParameterValue: (paramName: string, value: any) => void;
  /** 批量设置参数值 */
  setParameterValues: (values: TTSParameterValues) => void;
  /** 重置为默认值 */
  resetToDefaults: () => void;
  /** 切换高级选项显示 */
  toggleAdvanced: () => void;
  /** 验证参数 */
  validateParameters: () => { isValid: boolean; errors: string[] };
}

export function useTTSParameters(engineType: string): UseTTSParametersReturn {
  const [parametersConfig, setParametersConfig] = useState<TTSParametersConfig>(
    {}
  );
  const [parameterValues, setParameterValues] = useState<TTSParameterValues>(
    {}
  );
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取参数配置
  const fetchParametersConfig = useCallback(async (engine: string) => {
    if (!engine) return;

    setLoading(true);
    setError(null);

    try {
      const config = await ttsApi.getEngineParameters(engine);
      setParametersConfig(config);

      // 初始化默认值
      const defaultValues: TTSParameterValues = {};
      Object.entries(config).forEach(([paramName, paramConfig]) => {
        defaultValues[paramName] = paramConfig.default;
      });
      setParameterValues(defaultValues);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : '获取参数配置失败';
      setError(errorMessage);
      console.error('获取参数配置失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 设置单个参数值
  const setParameterValue = useCallback((paramName: string, value: any) => {
    setParameterValues((prev) => ({
      ...prev,
      [paramName]: value,
    }));
  }, []);

  // 批量设置参数值
  const batchSetParameterValues = useCallback((values: TTSParameterValues) => {
    setParameterValues(values);
  }, []);

  // 重置为默认值
  const resetToDefaults = useCallback(() => {
    const defaultValues: TTSParameterValues = {};
    Object.entries(parametersConfig).forEach(([paramName, paramConfig]) => {
      defaultValues[paramName] = paramConfig.default;
    });
    setParameterValues(defaultValues);
  }, [parametersConfig]);

  // 切换高级选项显示
  const toggleAdvanced = useCallback(() => {
    setShowAdvanced((prev) => !prev);
  }, []);

  // 验证参数
  const validateParameters = useCallback(() => {
    return ttsApi.validateParameters(parameterValues, parametersConfig);
  }, [parameterValues, parametersConfig]);

  // 引擎类型变化时重新获取配置
  useEffect(() => {
    fetchParametersConfig(engineType);
  }, [engineType, fetchParametersConfig]);

  return {
    parametersConfig,
    parameterValues,
    showAdvanced,
    loading,
    error,
    setParameterValue,
    setParameterValues: batchSetParameterValues,
    resetToDefaults,
    toggleAdvanced,
    validateParameters,
  };
}

import type { TTSParameterValues } from '../types/tts';
import { useTTSParameters } from '../hooks/useTTSParameters';
import DynamicParameterGroup from './DynamicParameterGroup';

interface TTSParametersPanelV2Props {
  /** TTS引擎类型 */
  engineType: string;
  /** 参数变化回调 */
  onParametersChange: (parameters: TTSParameterValues) => void;
  /** 是否显示参数验证错误 */
  showValidationErrors?: boolean;
}

export default function TTSParametersPanelV2({
  engineType,
  onParametersChange,
  showValidationErrors = false,
}: TTSParametersPanelV2Props) {
  const {
    parametersConfig,
    parameterValues,
    showAdvanced,
    loading,
    error,
    setParameterValue,
    resetToDefaults,
    toggleAdvanced,
    validateParameters,
  } = useTTSParameters(engineType);

  // 参数变化处理
  const handleParameterChange = (paramName: string, value: never) => {
    setParameterValue(paramName, value);

    // 更新后的参数值
    const updatedValues = { ...parameterValues, [paramName]: value };
    onParametersChange(updatedValues);
  };

  // 验证结果
  const validation = validateParameters();

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <span className="loading loading-spinner loading-md"></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="alert alert-error">
          <span>获取参数配置失败: {error}</span>
        </div>
      </div>
    );
  }

  if (Object.keys(parametersConfig).length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>当前引擎没有可配置的参数</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 头部工具栏 */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-3">
        <h2 className="text-xl font-semibold text-gray-800">语音参数</h2>
        <div className="flex gap-2">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              className="checkbox checkbox-sm"
              checked={showAdvanced}
              onChange={toggleAdvanced}
            />

            <span className="text-sm text-gray-600">显示高级选项</span>
          </label>
          <button className="btn btn-sm btn-outline" onClick={resetToDefaults}>
            重置
          </button>
        </div>
      </div>

      {/* 参数验证错误提示 */}
      {showValidationErrors && !validation.isValid && (
        <div className="alert alert-warning">
          <div>
            <h3 className="font-bold">参数配置有误:</h3>
            <ul className="mt-2 list-disc list-inside">
              {validation.errors.map((error, index) => (
                <li key={index} className="text-sm">
                  {error}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* 动态参数组 */}
      <DynamicParameterGroup
        parametersConfig={parametersConfig}
        values={parameterValues}
        onChange={handleParameterChange}
        showAdvanced={showAdvanced}
      />

      {/* 参数摘要（可选） */}
      {Object.keys(parameterValues).length > 0 && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">当前配置</h3>
          <div className="text-xs text-gray-600 space-y-1">
            {Object.entries(parameterValues).map(([key, value]) => {
              const config = parametersConfig[key];
              if (!config || (config.advanced && !showAdvanced)) return null;

              const displayValue =
                typeof value === 'boolean'
                  ? value
                    ? '开启'
                    : '关闭'
                  : `${value}${config.unit || ''}`;

              return (
                <div key={key} className="flex justify-between">
                  <span>{config.label}:</span>
                  <span className="font-mono">{displayValue}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

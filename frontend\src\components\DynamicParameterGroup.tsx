import { useMemo } from 'react';
import type {
  TTSParametersConfig,
  TTSParameterValues,
  ParameterCategory,
} from '../types/tts';
import { PARAMETER_CATEGORIES } from '../types/tts';
import DynamicParameterControl from './DynamicParameterControl';

interface DynamicParameterGroupProps {
  /** 参数配置 */
  parametersConfig: TTSParametersConfig;
  /** 当前参数值 */
  values: TTSParameterValues;
  /** 值变化回调 */
  onChange: (paramName: string, value: any) => void;
  /** 是否显示高级参数 */
  showAdvanced?: boolean;
}

export default function DynamicParameterGroup({
  parametersConfig,
  values,
  onChange,
  showAdvanced = false,
}: DynamicParameterGroupProps) {
  // 检查参数是否应该显示（基于依赖关系）
  const isParameterVisible = (paramName: string): boolean => {
    const config = parametersConfig[paramName];
    if (!config) return false;

    // 检查高级参数
    if (config.advanced && !showAdvanced) {
      return false;
    }

    // 检查依赖关系
    if (config.depends_on) {
      for (const [depParam, depValue] of Object.entries(config.depends_on)) {
        const currentValue = values[depParam];
        if (currentValue !== depValue) {
          return false;
        }
      }
    }

    return true;
  };

  // 按分类分组参数
  const groupedParameters = useMemo(() => {
    const groups: Record<string, string[]> = {};

    Object.keys(parametersConfig).forEach((paramName) => {
      if (!isParameterVisible(paramName)) return;

      const config = parametersConfig[paramName];
      const category = config.category || 'basic';

      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(paramName);
    });

    return groups;
  }, [parametersConfig, values, showAdvanced]);

  // 渲染参数分组
  const renderParameterGroup = (category: string, paramNames: string[]) => {
    if (paramNames.length === 0) return null;

    const categoryLabel =
      PARAMETER_CATEGORIES[category as ParameterCategory] || category;

    return (
      <div key={category} className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700 border-b border-gray-200 pb-2">
          {categoryLabel}
        </h3>
        <div className="space-y-4">
          {paramNames.map((paramName) => {
            const config = parametersConfig[paramName];
            return (
              <DynamicParameterControl
                key={paramName}
                paramName={paramName}
                config={config}
                value={values[paramName]}
                onChange={onChange}
              />
            );
          })}
        </div>
      </div>
    );
  };

  // 按分类顺序渲染
  const categoryOrder: ParameterCategory[] = [
    'basic',
    'voice',
    'performance',
    'advanced',
  ];

  return (
    <div className="space-y-6">
      {categoryOrder.map((category) => {
        const paramNames = groupedParameters[category];
        return paramNames ? renderParameterGroup(category, paramNames) : null;
      })}

      {/* 渲染其他未分类的参数 */}
      {Object.entries(groupedParameters).map(([category, paramNames]) => {
        if (categoryOrder.includes(category as ParameterCategory)) return null;
        return renderParameterGroup(category, paramNames);
      })}
    </div>
  );
}

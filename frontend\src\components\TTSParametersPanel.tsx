import { useState, useEffect } from 'react';
import type { TTSParametersConfig, TTSParameterValues } from '../types/tts';
import { ttsApi } from '../services/ttsApi';
import DynamicParameterGroup from './DynamicParameterGroup';

interface TTSParametersPanelProps {
  /** TTS引擎类型 */
  engineType: string;
  /** 参数变化回调 */
  onParametersChange: (parameters: TTSParameterValues) => void;
}

export default function TTSParametersPanel({
  engineType,
  onParametersChange,
}: TTSParametersPanelProps) {
  const [parametersConfig, setParametersConfig] = useState<TTSParametersConfig>(
    {}
  );
  const [parameterValues, setParameterValues] = useState<TTSParameterValues>(
    {}
  );
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [loading, setLoading] = useState(false);

  // 从后端API获取参数配置
  const fetchParametersConfig = async (engine: string) => {
    setLoading(true);
    try {
      // 调用实际的API
      const config = await ttsApi.getEngineParameters(engine);
      setParametersConfig(config);

      // 初始化默认值
      const defaultValues: TTSParameterValues = {};
      Object.entries(config).forEach(([paramName, paramConfig]) => {
        defaultValues[paramName] = paramConfig.default;
      });
      setParameterValues(defaultValues);
      onParametersChange(defaultValues);
    } catch (error) {
      console.error('获取参数配置失败:', error);
      // 使用模拟数据作为fallback
      const mockConfig = getMockConfig(engine);
      setParametersConfig(mockConfig);

      const defaultValues: TTSParameterValues = {};
      Object.entries(mockConfig).forEach(([paramName, paramConfig]) => {
        defaultValues[paramName] = paramConfig.default;
      });
      setParameterValues(defaultValues);
      onParametersChange(defaultValues);
    }
    setLoading(false);
  };

  // 模拟数据（用于开发测试）
  const getMockConfig = (engine: string): TTSParametersConfig => {
    if (engine === 'edge-tts') {
      return {
        rate: {
          type: 'int',
          control: 'slider',
          label: '语速',
          range: [-100, 100],
          default: 0,
          description: '语速调节，正值加快，负值减慢',
          unit: '%',
          step: 5,
          category: 'basic',
          advanced: false,
        },
        volume: {
          type: 'int',
          control: 'slider',
          label: '音量',
          range: [-100, 100],
          default: 0,
          description: '音量调节，正值提高，负值降低',
          unit: '%',
          step: 5,
          category: 'basic',
          advanced: false,
        },
        pitch: {
          type: 'int',
          control: 'slider',
          label: '音调',
          range: [-100, 100],
          default: 0,
          description: '音调调节，正值提高，负值降低',
          unit: 'Hz',
          step: 5,
          category: 'basic',
          advanced: false,
        },
      };
    } else if (engine === 'index-tts') {
      return {
        enable_fast_inference: {
          type: 'bool',
          control: 'switch',
          label: '快速推理',
          default: false,
          description: '开启快速推理模式，可以提升2-10倍速度，适合长文本合成',
          category: 'performance',
          advanced: false,
        },
        max_text_tokens_per_sentence: {
          type: 'int',
          control: 'slider',
          label: '分句Token数',
          range: [30, 300],
          default: 100,
          description:
            '每个句子的最大token数，较大值适合长句子，较小值适合短句子',
          step: 5,
          category: 'performance',
          advanced: true,
          depends_on: { enable_fast_inference: true },
        },
        sentences_bucket_max_size: {
          type: 'int',
          control: 'slider',
          label: '分桶大小',
          range: [1, 10],
          default: 4,
          description:
            '句子分桶的最大容量，用于批量处理优化，较大值可能提升速度但占用更多内存',
          step: 1,
          category: 'performance',
          advanced: true,
          depends_on: { enable_fast_inference: true },
        },
      };
    }
    return {};
  };

  // 参数值变化处理
  const handleParameterChange = (paramName: string, value: any) => {
    const newValues = { ...parameterValues, [paramName]: value };
    setParameterValues(newValues);
    onParametersChange(newValues);
  };

  // 重置为默认值
  const handleReset = () => {
    const defaultValues: TTSParameterValues = {};
    Object.entries(parametersConfig).forEach(([paramName, paramConfig]) => {
      defaultValues[paramName] = paramConfig.default;
    });
    setParameterValues(defaultValues);
    onParametersChange(defaultValues);
  };

  // 引擎类型变化时重新获取配置
  useEffect(() => {
    if (engineType) {
      fetchParametersConfig(engineType);
    }
  }, [engineType]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <span className="loading loading-spinner loading-md"></span>
      </div>
    );
  }

  if (Object.keys(parametersConfig).length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>当前引擎没有可配置的参数</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 头部工具栏 */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-3">
        <h2 className="text-xl font-semibold text-gray-800">语音参数</h2>
        <div className="flex gap-2">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              className="checkbox checkbox-sm"
              checked={showAdvanced}
              onChange={(e) => setShowAdvanced(e.target.checked)}
            />

            <span className="text-sm text-gray-600">显示高级选项</span>
          </label>
          <button className="btn btn-sm btn-outline" onClick={handleReset}>
            重置
          </button>
        </div>
      </div>

      {/* 动态参数组 */}
      <DynamicParameterGroup
        parametersConfig={parametersConfig}
        values={parameterValues}
        onChange={handleParameterChange}
        showAdvanced={showAdvanced}
      />
    </div>
  );
}

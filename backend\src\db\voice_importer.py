"""
语音元数据导入工具
"""

import json
from pathlib import Path
from typing import Any, Dict, List

from ..utils.logging_config import get_logger
from .voice_meta import VoiceMetadata, VoiceMetaDB

logger = get_logger(__name__)


class VoiceMetaImporter:
    """语音元数据导入器"""

    def __init__(self, db_path: str = None):
        self.db = VoiceMetaDB(db_path)

    def import_from_json_file(self, json_file_path: str) -> bool:
        """从JSON文件导入语音元数据"""
        try:
            logger.info(f"开始从JSON文件导入语音元数据: {json_file_path}")
            with open(json_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            return self.import_from_json_data(data)
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
            return False

    def import_from_json_data(self, json_data: List[Dict[str, Any]]) -> bool:
        """从JSON数据导入语音元数据"""
        success_count = 0
        total_count = len(json_data)
        logger.info(f"开始从JSON数据导入语音元数据，共 {total_count} 条记录")

        for item in json_data:
            try:
                voice = VoiceMetadata(
                    id_name=item["id_name"],
                    gender=item["gender"],
                    display_name=item["display_name"],
                    locale=item["locale"],
                    age_group=item["age_group"],
                    tts_engine=item["tts_engine"],
                    description=item.get("description", ""),
                    styles=item.get("styles", {}),
                    exa=item.get("exa", {}),
                )

                if self.db.insert_voice_metadata(voice):
                    success_count += 1
                    logger.debug(f"成功导入: {voice.display_name} ({voice.id_name})")
                else:
                    logger.warning(f"导入失败: {voice.display_name} ({voice.id_name})")

            except Exception as e:
                logger.error(f"处理语音数据失败: {e}, 数据: {item}")

        logger.info(f"语音元数据导入完成: {success_count}/{total_count} 条记录成功导入")
        return success_count == total_count

    def export_to_json_file(self, output_file_path: str) -> bool:
        """导出数据库中的语音元数据到JSON文件"""
        try:
            logger.info(f"开始导出语音元数据到JSON文件: {output_file_path}")
            voices = self.db.get_all_voices()

            json_data = []
            for voice in voices:
                json_data.append(
                    {
                        "id_name": voice.id_name,
                        "gender": voice.gender,
                        "display_name": voice.display_name,
                        "locale": voice.locale,
                        "age_group": voice.age_group,
                        "tts_engine": voice.tts_engine,
                        "description": voice.description,
                        "styles": voice.styles,
                        "exa": voice.exa,
                    }
                )

            with open(output_file_path, "w", encoding="utf-8") as f:
                json.dump(json_data, f, ensure_ascii=False, indent=4)

            logger.info(f"成功导出 {len(json_data)} 条记录到 {output_file_path}")
            return True

        except Exception as e:
            logger.error(f"导出失败: {e}")
            return False


def main():
    """主函数，用于测试导入功能"""
    logger.info("开始执行 voice_importer.py 的主函数测试")
    # 示例：导入现有的meta.json文件
    importer = VoiceMetaImporter()

    # 导入数据
    json_file = "resources/TTS/voices/meta.json"
    if Path(json_file).exists():
        logger.info(f"开始导入 {json_file}")
        success = importer.import_from_json_file(json_file)
        logger.info(f"导入{'成功' if success else '失败'}")

        # 显示统计信息
        stats = importer.db.get_engines_stats()
        logger.info("\n各TTS引擎统计:")
        for engine, count in stats.items():
            logger.info(f"  {engine}: {count} 个语音")
    else:
        logger.warning(f"文件不存在: {json_file}")


if __name__ == "__main__":
    main()

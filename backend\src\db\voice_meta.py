"""
语音元数据数据库模型和操作
"""

import json
import sqlite3
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from ..utils.logging_config import get_logger
from .config import get_database_path

logger = get_logger(__name__)


@dataclass
class VoiceMetadata:
    """语音元数据数据类"""

    id_name: str
    gender: str
    display_name: str
    locale: str
    age_group: str
    tts_engine: str
    description: str
    styles: Dict[str, str]
    exa: Dict[str, Any]


class VoiceMetaDB:
    """语音元数据数据库操作类"""

    def __init__(self, db_path: str = None):
        self.db_path = db_path or get_database_path()
        self.init_db()

    def init_db(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建优化的语音元数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS voice_metadata (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        id_name TEXT NOT NULL,
                        tts_engine TEXT NOT NULL,
                        gender TEXT NOT NULL,
                        display_name TEXT NOT NULL,
                        locale TEXT NOT NULL,
                        age_group TEXT NOT NULL,
                        description TEXT,
                        styles_json TEXT NOT NULL,  -- JSON格式存储styles
                        exa_json TEXT,              -- JSON格式存储exa
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        
                        -- 唯一约束：同一TTS引擎下的id_name必须唯一
                        UNIQUE(id_name, tts_engine)
                    )
                """)

                # 创建复合索引优化查询性能
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_id_name_tts_engine 
                    ON voice_metadata(id_name, tts_engine)
                """)

                # 创建单字段索引
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_tts_engine 
                    ON voice_metadata(tts_engine)
                """)

                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_gender_age 
                    ON voice_metadata(gender, age_group)
                """)

                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_locale 
                    ON voice_metadata(locale)
                """)

                conn.commit()
                logger.info(
                    f"数据库表 'voice_metadata' 创建/检查完成，路径: {self.db_path}"
                )
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise

    def insert_voice_metadata(self, voice: VoiceMetadata) -> bool:
        """插入语音元数据"""
        try:
            logger.debug(
                f"尝试插入或更新语音元数据: {voice.display_name} ({voice.id_name})"
            )
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO voice_metadata 
                    (id_name, tts_engine, gender, display_name, locale, age_group, 
                     description, styles_json, exa_json, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """,
                    (
                        voice.id_name,
                        voice.tts_engine,
                        voice.gender,
                        voice.display_name,
                        voice.locale,
                        voice.age_group,
                        voice.description,
                        json.dumps(voice.styles, ensure_ascii=False),
                        json.dumps(voice.exa, ensure_ascii=False),
                    ),
                )
                conn.commit()
                logger.debug(f"成功插入或更新语音元数据: {voice.display_name}")
                return True
        except sqlite3.IntegrityError as e:
            logger.warning(
                f"插入语音元数据失败，可能存在重复记录: {voice.id_name}, 错误: {e}"
            )
            return False
        except Exception as e:
            logger.error(f"插入语音元数据失败: {e}")
            return False

    def get_voice_by_id_and_engine(
        self, id_name: str, tts_engine: str
    ) -> Optional[VoiceMetadata]:
        """根据id_name和tts_engine查询语音元数据"""
        logger.debug(f"查询语音元数据: id_name='{id_name}', tts_engine='{tts_engine}'")
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT id_name, tts_engine, gender, display_name, locale, age_group,
                       description, styles_json, exa_json
                FROM voice_metadata 
                WHERE id_name = ? AND tts_engine = ?
            """,
                (id_name, tts_engine),
            )

            row = cursor.fetchone()
            if row:
                voice = VoiceMetadata(
                    id_name=row[0],
                    tts_engine=row[1],
                    gender=row[2],
                    display_name=row[3],
                    locale=row[4],
                    age_group=row[5],
                    description=row[6],
                    styles=json.loads(row[7]) if row[7] else {},
                    exa=json.loads(row[8]) if row[8] else {},
                )
                logger.debug(f"找到语音元数据: {voice.display_name}")
                return voice
            logger.debug(
                f"未找到语音元数据: id_name='{id_name}', tts_engine='{tts_engine}'"
            )
            return None

    def get_voices_by_engine(self, tts_engine: str) -> List[VoiceMetadata]:
        """根据TTS引擎查询所有语音"""
        logger.debug(f"查询引擎 '{tts_engine}' 的所有语音元数据")
        voices = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT id_name, tts_engine, gender, display_name, locale, age_group,
                       description, styles_json, exa_json
                FROM voice_metadata 
                WHERE tts_engine = ?
                ORDER BY gender, age_group, display_name
            """,
                (tts_engine,),
            )

            for row in cursor.fetchall():
                voices.append(
                    VoiceMetadata(
                        id_name=row[0],
                        tts_engine=row[1],
                        gender=row[2],
                        display_name=row[3],
                        locale=row[4],
                        age_group=row[5],
                        description=row[6],
                        styles=json.loads(row[7]) if row[7] else {},
                        exa=json.loads(row[8]) if row[8] else {},
                    )
                )
        logger.debug(f"引擎 '{tts_engine}' 找到 {len(voices)} 条语音元数据")
        return voices

    def get_voices_by_filters(
        self,
        tts_engine: Optional[str] = None,
        gender: Optional[str] = None,
        age_group: Optional[str] = None,
        locale: Optional[str] = None,
    ) -> List[VoiceMetadata]:
        """根据多个条件筛选语音"""
        logger.debug(
            f"根据过滤器查询语音: 引擎={tts_engine}, 性别={gender}, 年龄={age_group}, 地区={locale}"
        )
        conditions = []
        params = []

        if tts_engine:
            conditions.append("tts_engine = ?")
            params.append(tts_engine)

        if gender:
            conditions.append("gender = ?")
            params.append(gender)

        if age_group:
            conditions.append("age_group = ?")
            params.append(age_group)

        if locale:
            conditions.append("locale = ?")
            params.append(locale)

        where_clause = " AND ".join(conditions) if conditions else "1=1"

        voices = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                f"""
                SELECT id_name, tts_engine, gender, display_name, locale, age_group,
                       description, styles_json, exa_json
                FROM voice_metadata 
                WHERE {where_clause}
                ORDER BY tts_engine, gender, age_group, display_name
            """,
                params,
            )

            for row in cursor.fetchall():
                voices.append(
                    VoiceMetadata(
                        id_name=row[0],
                        tts_engine=row[1],
                        gender=row[2],
                        display_name=row[3],
                        locale=row[4],
                        age_group=row[5],
                        description=row[6],
                        styles=json.loads(row[7]) if row[7] else {},
                        exa=json.loads(row[8]) if row[8] else {},
                    )
                )
        logger.debug(f"根据过滤器查询到 {len(voices)} 条语音元数据")
        return voices

    def get_all_voices(self) -> List[VoiceMetadata]:
        """获取所有语音元数据"""
        logger.debug("获取所有语音元数据")
        return self.get_voices_by_filters()

    def delete_voice(self, id_name: str, tts_engine: str) -> bool:
        """删除指定语音元数据"""
        logger.info(f"请求删除语音: id_name='{id_name}', tts_engine='{tts_engine}'")
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    DELETE FROM voice_metadata 
                    WHERE id_name = ? AND tts_engine = ?
                """,
                    (id_name, tts_engine),
                )
                conn.commit()
                if cursor.rowcount > 0:
                    logger.info(f"成功删除语音: {id_name} ({tts_engine})")
                    return True
                else:
                    logger.warning(
                        f"未找到要删除的语音或删除失败: {id_name} ({tts_engine})"
                    )
                    return False
        except Exception as e:
            logger.error(f"删除语音失败: {id_name} ({tts_engine}), 错误: {e}")
            return False

    def get_voice_styles(self, id_name: str, tts_engine: str) -> Dict[str, str]:
        """获取指定语音的所有样式"""
        logger.debug(f"获取语音 '{id_name}' ({tts_engine}) 的样式")
        voice = self.get_voice_by_id_and_engine(id_name, tts_engine)
        if voice:
            logger.debug(f"找到语音样式: {voice.styles}")
            return voice.styles
        logger.warning(f"未找到语音 '{id_name}' ({tts_engine}) 的样式")
        return {}

    def get_engines_stats(self) -> Dict[str, int]:
        """获取各TTS引擎的语音数量统计"""
        logger.info("获取各TTS引擎的语音数量统计")
        stats = {}
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT tts_engine, COUNT(*) FROM voice_metadata GROUP BY tts_engine"
            )
            for row in cursor.fetchall():
                stats[row[0]] = row[1]
        logger.info(f"TTS引擎统计完成: {stats}")
        return stats

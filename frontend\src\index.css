@import 'tailwindcss';
@plugin "daisyui";

/* 定义自定义主题 */
@theme {
  /* 主色系 - 深蓝渐变系 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  /* 辅助色系 - 紫色系 */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;
  --color-secondary-950: #3b0764;

  /* 强调色系 - 青色 */
  --color-accent-50: #ecfeff;
  --color-accent-100: #cffafe;
  --color-accent-200: #a5f3fc;
  --color-accent-300: #67e8f9;
  --color-accent-400: #22d3ee;
  --color-accent-500: #06b6d4;
  --color-accent-600: #0891b2;
  --color-accent-700: #0e7490;
  --color-accent-800: #155e75;
  --color-accent-900: #164e63;
  --color-accent-950: #083344;

  /* 成功色系 */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-success-950: #052e16;

  /* 警告色系 */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  --color-warning-950: #451a03;

  /* 错误色系 */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error-950: #450a0a;

  /* 字体配置 */
  --font-family-sans: 'Inter', 'system-ui', 'sans-serif';
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'monospace';

  /* 阴影配置 */
  --shadow-glow: 0 0 20px rgb(59 130 246 / 0.15);
  --shadow-glow-lg: 0 0 40px rgb(59 130 246 / 0.25);

  /* 间距配置 */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;
  --spacing-128: 32rem;

  /* 边框圆角 */
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
}

/* 全局样式重置和基础设置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    @apply h-full;
  }

  body {
    @apply h-full bg-gray-50 text-gray-900 font-sans antialiased;
    overflow: hidden;
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

/* 组件样式 */
@layer components {
  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
  }

  .card-glass {
    @apply bg-white/80 backdrop-blur-md border border-white/20;
  }

  /* 面板组件 */
  .panel {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden;
  }

  .panel-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50/50;
  }

  .panel-body {
    @apply p-6;
  }

  .panel-title {
    @apply text-lg font-semibold text-gray-900 flex items-center gap-3;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
  }

  .btn-accent {
    @apply bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500 shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
  }

  .btn-error {
    @apply bg-error-500 text-white hover:bg-error-600 focus:ring-error-500;
  }

  .btn-ghost {
    @apply bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  .input-sm {
    @apply px-2.5 py-1.5 text-sm;
  }

  .input-lg {
    @apply px-4 py-3 text-lg;
  }

  .input-error {
    @apply border-error-300 focus:ring-error-500;
  }

  /* 文本区域 */
  .textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 resize-none font-mono leading-relaxed;
  }

  /* 选择框 */
  .select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 cursor-pointer;
  }

  /* 标签样式 */
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1.5;
  }

  /* 字段集样式 - daisyUI 5 推荐 */
  .fieldset {
    @apply space-y-2;
  }

  .label-required::after {
    @apply text-error-500 ml-1;
    content: '*';
  }

  /* 滑块组件 */
  .slider {
    @apply w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .slider::-webkit-slider-thumb {
    @apply appearance-none w-5 h-5 bg-primary-600 rounded-full cursor-pointer border-2 border-white shadow-lg;
  }

  .slider::-moz-range-thumb {
    @apply w-5 h-5 bg-primary-600 rounded-full cursor-pointer border-2 border-white shadow-lg;
  }

  /* 进度条 */
  .progress {
    @apply w-full bg-gray-200 rounded-full overflow-hidden;
  }

  .progress-bar {
    @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-300 ease-out;
  }

  .progress-sm {
    @apply h-1;
  }

  .progress-md {
    @apply h-2;
  }

  .progress-lg {
    @apply h-3;
  }

  /* 徽章和标签 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-error-100 text-error-800;
  }

  .badge-neutral {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
  }

  /* 分隔线 */
  .divider {
    @apply border-t border-gray-200 my-4;
  }

  .divider-vertical {
    @apply border-l border-gray-200 mx-4 h-full;
  }

  /* 标题栏样式 */
  .title-bar {
    @apply h-10 bg-gray-100/80 backdrop-blur-md border-b border-gray-200 flex items-center px-4 text-sm font-medium text-gray-700;
  }

  /* 音频播放器样式 */
  .audio-player {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden;
  }

  .audio-controls {
    @apply flex items-center gap-4;
  }

  .audio-time {
    @apply text-sm font-mono text-gray-500;
  }

  /* 角色配置项样式 */
  .character-item {
    @apply p-4 border border-gray-200 rounded-lg bg-white hover:border-gray-300 hover:shadow-sm transition-all duration-200;
  }

  .character-item-active {
    @apply border-primary-300 bg-primary-50 shadow-sm;
  }

  /* 动画和过渡 */
  .animate-in {
    animation: fadeIn 0.3s ease-out;
  }

  .slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-20px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* 工具类 */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgb(59 130 246 / 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgb(59 130 246 / 0.4);
  }
}

/* 响应式设计 - 使用 Tailwind CSS v4 @utility API */
@utility scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
@utility scrollbar-hide {
  &::-webkit-scrollbar {
    display: none;
  }
}

@utility text-ellipsis {
  @apply truncate;
}

@utility bg-gradient-primary {
  @apply bg-gradient-to-br from-primary-50 to-primary-100;
}

@utility bg-gradient-app {
  @apply bg-gradient-to-br from-gray-50 via-white to-gray-100;
}

@utility grid-app {
  @apply grid grid-cols-[280px_1fr_300px] gap-2;

  @media (max-width: 1400px) {
    @apply grid-cols-[260px_1fr_280px] gap-2;
  }

  @media (max-width: 1200px) {
    @apply grid-cols-[240px_1fr_260px] gap-2;
  }

  @media (max-width: 1024px) {
    @apply grid-cols-1 gap-2;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .dark {
    @apply bg-gray-900 text-gray-100;
  }

  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .panel-header {
    @apply bg-gray-800/50 border-gray-700;
  }

  .dark .input {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }

  .dark .title-bar {
    @apply bg-gray-800/80 border-gray-700 text-gray-300;
  }
}

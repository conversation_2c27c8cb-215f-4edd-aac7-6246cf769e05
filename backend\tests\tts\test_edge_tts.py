import json
import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))

from src.tts.edge.edge_engine import EdgeTTS


def test_get_voices():
    """测试获取Edge TTS语音列表"""
    edge_tts = EdgeTTS()
    voices = edge_tts.get_voices(locales=["zh"])
    print(f"Edge TTS可用语音数量: {len(voices)}")
    print(f"Edge TTS语音列表: {json.dumps(voices, ensure_ascii=False)}")


if __name__ == "__main__":
    test_get_voices()

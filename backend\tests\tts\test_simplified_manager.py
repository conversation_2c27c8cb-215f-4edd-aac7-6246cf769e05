"""
测试简化后的TTS管理器
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))

from tts.manager import TTSManager


def test_simplified_tts_manager():
    """测试简化后的TTS管理器基本功能"""
    # 创建管理器实例
    manager = TTSManager()

    # 验证单例模式
    manager2 = TTSManager()
    assert manager is manager2, "TTSManager应该是单例模式"

    # 验证可以获取可用引擎列表
    engines = manager.list_available_engines()
    assert isinstance(engines, list), "应该返回引擎列表"

    print(f"可用引擎: {engines}")

    # 如果有edge引擎，测试获取引擎实例
    if "edge" in engines:
        engine = manager.get_engine("edge")
        assert engine is not None, "应该能获取edge引擎实例"

        # 测试引擎信息
        info = engine.get_engine_info()
        assert isinstance(info, dict), "引擎信息应该是字典类型"
        print(f"Edge引擎信息: {info}")


if __name__ == "__main__":
    test_simplified_tts_manager()
    print("简化测试通过！")

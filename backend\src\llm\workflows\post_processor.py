"""后处理模块

包含角色优化、情感推断等后处理功能
"""

from datetime import datetime

from ...utils.logging_config import get_logger

logger = get_logger(__name__)

from .langsmith_config import log_workflow_metrics, with_langsmith_tracing  # noqa: E402


class PostProcessor:
    """后处理器"""

    def __init__(self, workflow_instance):
        """
        初始化后处理器

        Args:
            workflow_instance: 主工作流实例，用于访问共享方法
        """
        self.workflow = workflow_instance

    @with_langsmith_tracing(
        run_name="post_process",
        tags=["post_processing", "data_refinement"],
        exclude_fields=[
            "novel_content",
            "chapter_contents",
            "overall_analysis",
            "chapters",
        ],
    )
    def post_process(self, state):
        """后处理节点：优化角色信息和处理不确定数据"""
        logger.info(f"{state['workflow_id']}: 开始后处理")

        # 记录后处理指标
        log_workflow_metrics(
            state,
            "post_process",
            {
                "characters_before": len(state["characters"]),
                "annotated_chapters_count": len(state["annotated_chapters"]),
            },
        )

        try:
            # 保存原始角色列表用于质量检查对比
            original_characters = state["characters"].copy()

            # 1. 从对话中提取实际出现的角色
            dialogue_characters = self._extract_characters_from_dialogues(
                state["annotated_chapters"]
            )

            # 2. 过滤和优化角色列表
            refined_characters = self._refine_character_list(
                state["characters"], dialogue_characters
            )

            # 3. 处理情感不确定的对话
            processed_chapters = self._process_uncertain_emotions(
                state["annotated_chapters"], refined_characters
            )

            # 4. 确保包含旁白角色并按重要性排序
            final_characters = self._ensure_narrator_and_sort_characters(
                refined_characters
            )

            # 记录后处理结果指标
            log_workflow_metrics(
                state,
                "post_process_results",
                {
                    "characters_after_refinement": len(refined_characters),
                    "characters_final": len(final_characters),
                    "dialogue_characters_found": len(dialogue_characters),
                    "narrator_added": len(final_characters) > len(refined_characters),
                    "uncertain_emotions_processed": sum(
                        1
                        for chapter in processed_chapters
                        for annotation in chapter.get("dialogue_annotations", [])
                        if annotation.get("emotion") != "uncertain"
                    ),
                },
                exclude_fields=[
                    "novel_content",
                    "chapter_contents",
                    "overall_analysis",
                ],
            )

            logger.info(
                f"{state['workflow_id']}: 后处理完成，角色数量：{len(state['characters'])} → {len(refined_characters)} → {len(final_characters)}"
            )

            return {
                **state,
                "step": "quality_check",
                "original_characters": original_characters,  # 保存原始角色用于质量检查
                "characters": final_characters,  # 使用经过旁白添加和排序的最终角色列表
                "annotated_chapters": processed_chapters,
                "updated_at": datetime.now().isoformat(),
                "processing_progress": 0.85,
            }

        except Exception as e:
            return self.workflow.handle_error(state, e)

    def _extract_characters_from_dialogues(self, chapters):
        """从对话标注中提取实际出现的角色"""
        dialogue_speakers = set()

        for chapter in chapters:
            annotations = chapter.get("dialogue_annotations", [])
            if annotations:  # 确保有标注数据
                for annotation in annotations:
                    if annotation.get("type") == "dialogue":
                        speaker = annotation.get("speaker")
                        if speaker and speaker not in [
                            "unknown",
                            "narrator",
                            "",
                            "Unknown",
                            "Narrator",
                        ]:
                            dialogue_speakers.add(speaker)

        logger.info(f"从对话中提取到的角色: {list(dialogue_speakers)}")
        return list(dialogue_speakers)

    def _refine_character_list(self, original_characters, dialogue_characters):
        """优化角色列表：保留在对话中实际出现的角色"""
        refined_characters = []

        # 创建角色名称到角色对象的映射
        char_map = {char.get("name", ""): char for char in original_characters}

        # 为对话中出现的角色匹配原始角色信息
        for speaker in dialogue_characters:
            if speaker in char_map:
                refined_characters.append(char_map[speaker])
                logger.debug(f"直接匹配角色: {speaker}")
            else:
                # 尝试模糊匹配（处理别名等情况）
                matched_char = self._fuzzy_match_character(speaker, original_characters)
                if matched_char:
                    refined_characters.append(matched_char)
                    logger.debug(
                        f"模糊匹配角色: {speaker} -> {matched_char.get('name', '')}"
                    )
                else:
                    # 创建简单的角色信息
                    new_character = {
                        "name": speaker,
                        "type": "unknown",
                        "description": f"从对话中提取的角色：{speaker}",
                        "personality": "未知",
                        "speech_style": "未知",
                        "aliases": [],
                        "source": "dialogue_extraction",
                    }
                    refined_characters.append(new_character)
                    logger.debug(f"创建新角色: {speaker}")

        # 如果没有从对话中找到任何角色，保留原始角色列表
        if not refined_characters:
            logger.warning("没有从对话中找到任何角色，保留原始角色列表")
            return original_characters

        return refined_characters

    def _process_uncertain_emotions(self, chapters, characters):
        """处理情感不确定的对话"""
        processed_chapters = []
        uncertain_count = 0
        processed_count = 0

        for chapter in chapters:
            annotations = chapter.get("dialogue_annotations", [])
            processed_annotations = []

            for annotation in annotations:
                if annotation.get("emotion") == "uncertain":
                    uncertain_count += 1
                    # 根据角色信息和上下文推断情感
                    inferred_emotion = self._infer_emotion(annotation, characters)
                    annotation = {
                        **annotation,
                        "emotion": inferred_emotion,
                        "emotion_inferred": True,
                    }
                    processed_count += 1

                processed_annotations.append(annotation)

            processed_chapter = {
                **chapter,
                "dialogue_annotations": processed_annotations,
            }
            processed_chapters.append(processed_chapter)

        if uncertain_count > 0:
            logger.info(
                f"处理了 {processed_count}/{uncertain_count} 个不确定情感的对话"
            )

        return processed_chapters

    def _fuzzy_match_character(self, speaker, characters):
        """模糊匹配角色（处理别名等）"""
        for char in characters:
            # 检查别名
            aliases = char.get("aliases", [])
            if speaker in aliases:
                return char

            # 检查部分匹配
            char_name = char.get("name", "")
            if speaker in char_name or char_name in speaker:
                return char

            # 检查去除空格后的匹配
            if speaker.replace(" ", "") == char_name.replace(" ", ""):
                return char

        return None

    def _infer_emotion(self, annotation, characters):
        """推断对话的情感
        TODO 是否应该去除？
        """
        content = annotation.get("content", "").lower()
        speaker = annotation.get("speaker", "")

        # 基于内容的关键词推断
        if any(word in content for word in ["！", "!", "？", "?"]):
            if any(word in content for word in ["不", "没", "别", "停", "不要"]):
                return "angry"
            elif any(word in content for word in ["哈", "呵", "嘿", "笑"]):
                return "happy"
            else:
                return "excited"
        elif any(word in content for word in ["…", "唉", "哎", "叹"]):
            return "sad"
        elif any(word in content for word in ["谢谢", "感谢", "太好了", "棒", "很好"]):
            return "happy"
        elif any(word in content for word in ["抱歉", "对不起", "不好意思", "sorry"]):
            return "apologetic"
        else:
            return "neutral"

    def _ensure_narrator_and_sort_characters(self, characters):
        """确保包含旁白角色并按重要性排序"""
        # 1. 检查是否已有旁白角色
        has_narrator = any(char.get("name") == "旁白" for char in characters)

        # 2. 如果没有旁白，添加标准旁白角色
        if not has_narrator:
            narrator_character = {
                "name": "旁白",
                "type": "narrator",
                "description": "小说的叙述者",
                "personality": "客观中性",
                "speech_style": "叙述性",
                "aliases": ["narrator", "叙述者"],
                "source": "system_added",
            }
            characters.append(narrator_character)
            logger.info("添加了系统旁白角色")

        # 3. 按重要性排序：旁白 -> 主角 -> 配角 -> 其他
        def get_sort_priority(char):
            char_type = char.get("type", "").lower()
            char_name = char.get("name", "")

            if char_name == "旁白":
                return 0  # 最高优先级
            elif char_type in ["主角", "protagonist", "main"]:
                return 1
            elif char_type in ["配角", "supporting", "secondary"]:
                return 2
            else:
                return 3  # 其他角色

        sorted_characters = sorted(characters, key=get_sort_priority)

        # 记录排序结果
        character_names = [char.get("name", "") for char in sorted_characters]
        logger.info(f"角色排序完成: {' → '.join(character_names)}")

        return sorted_characters
